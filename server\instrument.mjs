import { config } from "dotenv";
import path from "path";
import * as Sentry from "@sentry/node";

// Load environment variables first - only in development
const isRailwayDeployment = !!(
  process.env.RAILWAY_ENVIRONMENT ||
  process.env.RAILWAY_PROJECT_ID ||
  process.env.NIXPACKS_METADATA
);

if (!isRailwayDeployment && process.env.NODE_ENV !== "production") {
  config({ path: path.resolve(process.cwd(), ".env") });
}

// Initialize Sentry before any other imports
if (process.env.SENTRY_DSN) {
  Sentry.init({
    dsn: process.env.SENTRY_DSN,
    environment: process.env.NODE_ENV || "development",
    tracesSampleRate: 0.1,
  });
}
