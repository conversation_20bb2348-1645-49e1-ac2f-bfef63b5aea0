# Bidaible Agent Guide

## Project Overview

Bidaible is a sophisticated AI-powered construction bidding platform built with enterprise-grade TypeScript architecture. The system delivers "wow factor" bid analysis through advanced competitive intelligence, risk assessment, and predictive analytics.

## Development Commands

### Primary Commands
- `npm run dev` - Start development server (Node.js + Express backend with Vite frontend)
- `npm run dev:api` - Start API-only server (BIDAIBLE_TEST=1, no Vite). Used for security/integration tests.
- `npm run build` - Build for production (Vite build + esbuild server bundle)
- `npm start` - Start production server 
- `npm run check` - TypeScript type checking
- `npm run db:push` - Push database schema changes (Drizzle)

### Testing Commands
- `cd tests && npm test` - Run comprehensive test suite
- `cd tests && npm run test:wasabi` - Run Wasabi storage tests only
- `cd tests && npm run test:ai` - Run AI processing tests only
- Security tests (API-only):
  - `npm run dev:api` (in one shell)
  - In another shell: `node tests/test-rfq-update-access.js`, `node tests/test-docs-files-access.js`, `node tests/test-rate-limits.js`, `node tests/test-admin-routes.js`

### Environment
- **Port**: 5000 (development and production) — API-only tests often use 5001
- **Database**: PostgreSQL with Drizzle ORM
- **File Storage**: Wasabi S3-compatible storage
- **Authentication**: Dual system (Clerk Auth + JWT API keys)

### Security Testing Notes
- Use `npm run dev:api` to run server without Vite (prevents SPA from catching `/api/*` routes)
- Dev fixtures (development only) under `/api/test/*` help set up orgs/users/RFQs for tests
- Denial semantics: non-owner access may respond with 404 (to avoid existence leaks); tests accept 403/404
- CI job `security-tests` requires `TEST_DATABASE_URL` secret (Neon/PG)
## Tech Stack & Architecture

### Frontend (React 18 + TypeScript)
- **Build Tool**: Vite with HMR
- **Routing**: Wouter (lightweight client-side routing)
- **Styling**: Tailwind CSS + shadcn/ui components
- **State**: TanStack Query (server state) + React Context (global state)
- **Forms**: React Hook Form + Zod validation
- **Notifications**: React-Toastify integration with custom CSS styling

### Backend (Node.js + Express + TypeScript)
- **Runtime**: Node.js with ES modules
- **Database**: PostgreSQL + Drizzle ORM + 30+ strategic indexes
- **File Processing**: Multi-provider AI (Groq primary, OpenAI/Gemini fallback)
- **Authentication**: Clerk Auth + JWT API keys with RBAC
- **Caching**: In-memory TTL/LRU + strategic database indexes

### Key Services
- **AI Processing**: Multi-provider fallback (Groq → OpenAI → Gemini)
- **File Storage**: Wasabi S3-compatible object storage with organization-based isolation
- **PDF Processing**: 3-tier extraction (PDF.js → pdf-parse → Gemini Vision)
- **Progress Tracking**: Server-Sent Events (SSE) for real-time updates
- **RFQ Deadline Management**: Automated notification scheduling with countdown timers
- **Notification Scheduler**: Background service for deadline reminders (1 week, 3 days, 1 day before bid deadline)
- **Multi-Tenant Storage**: Organization-scoped file storage with duplicate prevention

## Code Style & Patterns

### TypeScript Configuration
- **Strict Mode**: Enabled for maximum type safety
- **ES Modules**: Used throughout the codebase
- **Type Safety**: End-to-end with Zod validation

### Database Patterns
- **Repository Pattern**: Clean data access layer separation
- **Strategic Indexing**: 30+ indexes for query optimization
- **Multi-tenant**: Organization-based data isolation

### Component Architecture
```
App (Root)
├── AuthenticatedApp (Protected Routes)
│   ├── Layout (Navigation + Sidebar)
│   ├── Pages (Route Components)
│   └── Components (Business Logic)
└── Landing (Public Routes)
```

### API Structure
```
/api/auth/*          # Authentication endpoints
/api/rfqs/*          # RFQ management
/api/contractors/*   # Contractor management  
/api/bids/*          # Bid management
/api/dashboard/*     # Dashboard statistics
```

## File Organization

### Project Structure
```
├── client/          # React frontend application
├── server/          # Express backend with TypeScript
├── shared/          # Shared types and utilities
├── docs/            # Comprehensive documentation
├── memory-bank/     # Project context and patterns
├── tests/           # Comprehensive test suite
└── migrations/      # Database migration files
```

### Key Files
- `server/routes.ts` - Main API route definitions
- `server/services/` - Business logic layer
- `server/storage.ts` - Database access layer
- `server/utils/typing.ts` - Typing helpers for Drizzle/Zod insert/update payloads
- `shared/types.ts` - TypeScript type definitions
- `drizzle.config.ts` - Database configuration
- `server/services/scheduledNotificationProcessor.ts` - RFQ deadline reminder processor
- `server/services/notificationService.ts` - Core notification service with deadline reminders
- `client/src/utils/countdown.ts` - Countdown utility functions and deadline validation
- `client/src/components/CountdownBadge.tsx` - Countdown display components with auto-refresh

## Environment Variables

### Required for Development
```env
# Database
DATABASE_URL=postgresql://username:password@host:port/database

# AI Services (at least one required)
GROQ_API_KEY=gsk_your_groq_api_key
OPENAI_API_KEY=sk-your_openai_api_key  
GEMINI_API_KEY=your_gemini_api_key

# Authentication
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key

# File Storage
WASABI_ACCESS_KEY_ID=your_wasabi_access_key
WASABI_SECRET_ACCESS_KEY=your_wasabi_secret_access_key
WASABI_BUCKET_NAME=bidaible-storage
WASABI_ENDPOINT=https://s3.wasabisys.com

# Application
NODE_ENV=development
PRIMARY_MODEL=groq
SESSION_SECRET=your_session_secret
JWT_SECRET=your_jwt_secret
```

Additional frontend env:
- `VITE_CLERK_PUBLISHABLE_KEY` — Clerk publishable key for the web app (pk_…). Required in production builds. The client also falls back to `window.__PUBLIC_ENV__` injected by the server at runtime.
- `VITE_CLERK_ACCOUNT_PORTAL_URL` — base URL for Clerk Account Portal direct links (e.g., `https://accounts.your-domain.com`). Optional; the UI falls back to `https://accounts.<host>` if unset.

## Development Workflow

### Getting Started
1. **Install dependencies**: `npm install`
2. **Configure environment**: Copy `.env.example` to `.env` and set values
3. **Setup database**: `npm run db:push`
4. **Start development**: `npm run dev`

### Working with Database
- **Schema changes**: Edit files in `server/schema/` then run `npm run db:push`
- **View data**: Use Drizzle Studio or database client
- **Indexes**: 30+ strategic indexes already configured for performance

### Testing Strategy
- **Unit Tests**: Service layer and utilities (in development)
- **Integration Tests**: Comprehensive test suite in `tests/` directory  
- **E2E Tests**: Critical user workflows (planned)
- **Performance Tests**: Load testing for scalability

## Key Features & Capabilities

### AI-Powered Processing
- **Document Analysis**: PDF, TXT, CSV support with 95%+ success rate
- **Competitive Intelligence**: Executive summaries with market analysis
- **Risk Assessment**: Automated bid scoring (0-100 scale)
- **Multi-Provider**: Groq (speed) → OpenAI (reliability) → Gemini (vision)

### RFQ Deadline Management System
- **Dual Deadline System**: Separate bid proposal deadlines and RFQ project deadlines with database-level validation
- **Automated Notification Scheduling**: 1 week, 3 days, and 1 day reminders scheduled automatically at RFQ creation
- **Background Processor**: `ScheduledNotificationProcessor` runs every minute to send pending deadline reminders
- **Real-time Countdown Timers**: Live countdown displays with color-coded urgency levels (green/yellow/red/gray)
- **Database Schema**: Enhanced with `bid_proposal_deadline_at` column and `scheduled_notifications` table
- **Client-side Validation**: Form validation prevents invalid deadline combinations
- **React-Toastify Integration**: Immediate user feedback for deadline-related actions and notifications

### Real-Time Notification System
- **Notification Bell Component**: Live notification dropdown in main navigation with unread count badge
- **Real-time Updates**: Polling every 30-60 seconds for live notification data via TanStack Query
- **Interactive Dropdown**: Click-to-navigate functionality directing users to relevant RFQs/bids
- **Mark as Read**: Individual and batch "mark as read" operations with instant UI updates
- **Comprehensive Notification Types**: Support for RFQ deadline reminders, bid submissions, status changes
- **Priority-based Display**: Color-coded priority levels (urgent/high/medium/low) with visual indicators
- **Full Notifications Page**: Dedicated notifications management with tabbed interface and filtering

### Multi-Tenant SaaS
- **Organization Isolation**: Complete data separation via Clerk
- **Role-Based Access**: Clerk organization roles (Admin, Editor, Viewer)
- **API Management**: JWT keys with scoped permissions
- **Audit Logging**: Comprehensive activity tracking

### File Processing Pipeline
```
Upload → Validation → Text Extraction → AI Analysis → Data Mapping → Storage
  ↓         ↓            ↓               ↓            ↓           ↓
Multi-   Size/Type    UnifiedPDF     Multi-model   Schema    Database
format   Checks       Extractor      Processing    Mapping   + Files
```

## Performance & Scalability

### Target Metrics
- **AI Processing**: Sub-3-second analysis generation
- **File Upload**: Support up to 50MB per file, 8 files per RFQ
- **Extraction**: 95%+ success rate for document processing
- **Concurrent Users**: Designed for 10,000+ users

### Optimization Strategies
- **Database**: 30+ strategic indexes for common queries
- **Caching**: Memory cache with TTL and LRU eviction
- **AI Fallback**: Multi-provider system ensures 100% reliability
- **Stream Processing**: Large file handling without memory issues

## Security Best Practices

### Authentication & Authorization
- **Dual Auth System**: Clerk (web) + JWT API keys (programmatic)
- **Session Authentication**: Frontend uses Clerk session cookies (`credentials: "include"`) 
- **Multi-tenant**: Organization-based data isolation with automatic user classification sync
- **Rate Limiting**: Per-user and per-API-key throttling
- **Audit Trails**: Comprehensive logging for compliance

### Data Protection  
- **Input Validation**: Zod schemas for all API inputs
- **SQL Injection**: Prevented via Drizzle ORM parameterized queries
- **File Security**: Type validation, size limits, secure storage
- **HTTPS**: All communications encrypted in production

## Common Patterns

### Error Handling
- **Graceful Degradation**: AI processing with multi-provider fallback
- **User-Friendly**: Meaningful error messages without system exposure
- **Logging**: Comprehensive error tracking with context
- **Recovery**: Automatic retry logic for transient failures

### State Management
- **Server State**: TanStack Query with caching and invalidation
- **Local State**: React hooks for component-specific state
- **Forms**: React Hook Form with Zod validation
- **Global Context**: React Context for theme and auth status

### API Patterns
- **RESTful**: Standard HTTP methods and status codes
- **Validation**: Request/response validation with Zod schemas
- **Middleware**: Security, authentication, rate limiting, caching
- **Documentation**: Self-documenting with TypeScript types

## Recent Updates & Status

### August 2025 Checkpoint
- **RFQ Archive System**: ✅ Complete archive management with ZIP bundling and organizational isolation
- **RFQ Deadline Management**: ✅ Comprehensive dual deadline system with automated reminders and countdown timers
- **Real-Time Notifications**: ✅ Live notification bell integration with polling and click-to-navigate functionality
- **Documentation**: Recently consolidated and deduplicated
- **Testing**: Comprehensive test suite implemented
- **Wasabi Migration**: Storage successfully migrated from Replit
- **Architecture**: Production-ready multi-tenant SaaS platform

### Recent Updates (August 26, 2025)
- **RFQ Archive System**: ✅ Comprehensive archive management for General Contractors
  - **Database Schema**: Added `isArchived` boolean field to `rfqs` table with strategic indexing
  - **Archive API Endpoints**: 
    - `PATCH /api/rfqs/:id/archive` - Archive single RFQ
    - `PATCH /api/rfqs/bulk-archive` - Archive multiple RFQs  
    - `PATCH /api/rfqs/:id/unarchive` - Unarchive RFQ
    - `GET /api/rfqs/archived` - Fetch archived RFQs
    - `GET /api/rfqs/:id/archive-download` - Download ZIP bundle
  - **ZIP Archive Downloads**: Complete project bundles with RFQ docs + all submitted bids
  - **Organization Isolation**: Only accessible by RFQ owner's organization
  - **Contractor Protection**: Archived RFQs completely hidden from contractors
  - **Audit Trail**: All archive actions logged for compliance
  - **UI Components**: Archive toggle, bulk operations, and download functionality
  - **File Structure**: `rfq-documents/org-{orgId}/rfq-{rfqId}/original/` + `bids/contractor-{name}/`
  - **Route Fix**: Fixed Express route conflict where `/api/rfqs/:id` was intercepting `/api/rfqs/archived` calls
  - **UI Fix**: Hidden Create RFQ button when viewing archived RFQs, updated empty state messaging

### Previous Fixes (August 21, 2025)
- **Organization-Based File Storage**: ✅ Implemented multi-tenant file organization with duplicate prevention
  - **New Folder Structure**: Files stored in `rfq-documents/org-{organizationId}/{rfqId?}/{timestamp}-{filename}`
  - **Eliminated Duplicate Uploads**: Fixed root cause where bid routes were calling `uploadFile()` twice
  - **Database Constraints**: Added unique indexes on `object_key` columns to prevent future duplicates
  - **Migration Ready**: Structure supports future RFQ archive features with bid bundling
- **Document Viewing Fix**: ✅ Resolved issue where uploaded documents weren't visible in UI
  - **Root Cause**: Frontend auth was sending Clerk JWT tokens as API keys (causing 401 errors)
  - **Authentication Fix**: Removed Authorization headers, now uses session cookies only (`credentials: "include"`)
  - **User Classification Sync**: Implemented automatic `userClassification` sync based on trade types
  - **View/Download Buttons**: Documents now display correctly with working view/download functionality
- **Automatic User Classification**: ✅ Added auto-sync of `userClassification` when contractor profiles update
  - **Logic**: "General Contractor" trade type → `userClassification: "general_contractor"`
  - **Logic**: Other trades (no GC) → `userClassification: "contractor"`
  - **Default**: No trades set → defaults to `"general_contractor"`
  - **Location**: Integrated into PUT `/api/contractors/profile` endpoint
  - **Safety**: Non-blocking sync won't fail profile updates

### Previous Fixes (August 15, 2025)
- **File Upload Flow**: ✅ Unified file processing working correctly
- **Storage Integration**: ✅ Wasabi S3 upload/storage confirmed functional
- **Database Updates**: ✅ RFQ records created with AI analysis data
- **Authentication Middleware**: ✅ Fixed `/api/rfqs` endpoint authentication (changed from `readOnlyApiAccess` to `protectedRoute + addUserToRequest`)
- **UI Display**: ✅ RFQs now visible in Dashboard and My RFQs pages
- **Terms & Conditions**: ✅ Fixed API endpoint mismatch (`/api/auth/accept-terms` → `/api/clerk/accept-terms`)
- **Organization UI**: ✅ Create Organization component now hides after organization creation (checks both Clerk and database organizationId)
- **Notifications Settings**: ✅ Implemented comprehensive notification preferences with bid management settings, delivery methods, and frequency controls
- **Audit Logs**: ✅ Implemented user activity audit trail with proper table format showing file uploads, RFQ creation, and system events
- **Codebase Cleanup**: ✅ Archived debugging scripts and removed redundant test files

### Known Issues
- **Organization Sync**: Clerk organization creation needs to sync with Bidaible database
- **Test Suite**: Some authentication integration tests need refinement
- **Performance**: Monitoring and optimization ongoing

### Technical Implementation Notes (August 20, 2025)

#### Bid Processing Pipeline Alignment
- **Issue**: Bid documents were failing AI analysis while RFQ documents worked correctly
- **Root Cause**: Bid processing used direct `unifiedPDFExtractor.extractText()` calls without OCR fallback
- **Solution**: Modified `extractBidPdfWithGroq()` and `extractBidPdfWithOpenAI()` to use `extractTextFromPdf()` method
- **Files Modified**: `server/services/aiService.ts` (lines 1207-1242)
- **Result**: Bid processing now handles scanned/image PDFs via Gemini Vision OCR fallback

#### Contact Auto-Population System
- **Issue**: Contact fields in bid submission form remained empty despite available profile data
- **Root Cause**: Query functions returned raw `Response` objects instead of parsed JSON data
- **Solution**: Replaced custom `apiRequest()` calls with `getQueryFn({ on401: "returnNull" })`
- **Files Modified**: `client/src/components/BidSubmissionForm.tsx` (lines 72-86)
- **Features Added**:
  - Primary: Contractor profile data auto-population
  - Fallback: Clerk user data if no contractor profile exists
  - Debug logging for troubleshooting

#### PDF Extraction Architecture
- **Unified Extractor**: `server/services/core/pdfExtractor.ts`
- **Extraction Chain**: PDF.js/pdf-parse → Gemini Vision OCR → Simple fallback
- **Environment Handling**: Production prioritizes pdf-parse, development uses PDF.js first
- **OCR Integration**: Gemini Vision API for scanned document text extraction

## Getting Help

### Documentation Locations
- `docs/` - Comprehensive technical documentation
- `memory-bank/` - Project context and architectural patterns  
- `tests/README.md` - Testing setup and execution guide
- `debug-archive/` - Archived debugging scripts and fix tools
- `DEPLOYMENT.md` - Production deployment instructions

### Common Issues & Solutions
1. **Build Errors**: Run `npm run check` for TypeScript validation
2. **Database Issues**: Verify DATABASE_URL and run `npm run db:push`
3. **API Failures**: Check environment variables and service status
4. **File Upload**: Ensure Wasabi credentials and bucket access
5. **RFQs Not Showing**: Check `/api/rfqs` endpoint returns 200 (not 401/403)
6. **Organization Issues**: User must have organizationId in database to see RFQs
7. **Test Auth Issues**: Use `/api/test/rfqs` endpoint for testing without auth

### Debugging Commands
- **Test API Connectivity**: `curl http://localhost:5000/api/waitlist/count`
- **Test File Upload**: `curl -X POST http://localhost:5000/api/test/rfqs -F "documents=@test.pdf" -F "projectName=Test"`
- **Test AI Processing**: Upload document via UI and verify AI summary appears in RFQ details
- **Check Environment**: `node debug-user-context.cjs` (debugging script)
- **Fix Test User**: `node fix-test-rfq-user.cjs` (associate test RFQs with real user)

### AI Processing Safeguards
- **Critical Files**: Never modify `server/services/aiOptimizedService.ts`, `server/services/core/pdfExtractor.ts`, or AI data flow in `server/routes.ts` without comprehensive testing
- **Required Testing**: Before any AI-related changes, test document upload → AI summary generation → UI display pipeline
- **Database Verification**: Check `ai_summary` column is populated after changes
- **Reference**: See `docs/AI_PROCESSING_SAFEGUARDS.md` for detailed protocols

---

## Development Notes – August 2025 (Typecheck stabilization + shared constants)

- Typecheck strategy
  - Introduced server-only TS config: `tsconfig.server.json` (checked via `npm run check`)
  - `npm run check` now runs `tsc -p tsconfig.server.json` to keep checks fast and reliable while we incrementally restore strict typing across the codebase
  - Plan A: fast green build achieved; Plan B: remove file-level suppressions and align types in small PRs

- Shared constants (frontend)
  - Centralized trade/specialty options in `client/src/constants/trades.ts`
  - Certification designations in `client/src/constants/certifications.ts` (MBE, WBE, DBE, SBE, VBE)
  - Consumers now import from the shared constant instead of inlining arrays
    - `client/src/pages/Settings.tsx`
    - `client/src/components/ContractorProfileForm.tsx`

- PDF Extraction (backend)
  - Unified extractor in `server/services/core/pdfExtractor.ts` with environment guards:
    - Production: prefer `pdf-parse` (no DOM APIs)
    - Development: try PDF.js first, fall back to `pdf-parse`, then Gemini Vision
  - `aiStreamProcessor` now delegates to the unified extractor and uses `fs.promises.readFile` for typed async I/O

- Notification Service (backend)
  - Removed broad type suppressions and aligned payloads with shared Insert* types
  - Insert flow:
    - `createNotification({ userId, type, title, message })`
    - Delivery records created via `createNotificationDelivery({ notificationId, deliveryMethod, recipient })`
    - Delivery status transitions via `updateDeliveryStatus(id, 'delivered' | 'failed', providerResponse)`
  - Audit logs for email delivery attempts/success/failure are written via `createBusinessAuditLog`

- RFQ Deadline Management System (backend + frontend)
  - Database schema changes in `migrations/0010_add_deadline_management.sql`:
    - Added `bid_proposal_deadline_at` column to `rfqs` table with validation constraints
    - Created `scheduled_notifications` table with status enum ('pending', 'sent', 'failed', 'cancelled')
    - Added strategic indexes for performance optimization
    - Constraint ensuring `bid_proposal_deadline_at < due_date`
  - Background processing via `ScheduledNotificationProcessor` running every minute
  - Automatic notification scheduling at RFQ creation (1 week, 3 days, 1 day before deadline)
  - Frontend countdown components with real-time updates and urgency color coding
  - Client-side deadline validation preventing invalid date combinations
  - React-Toastify integration for immediate user feedback on deadline-related actions

- Security middleware
  - Helmet options typed and deprecated flags removed in `server/middleware/security.ts`
  - `orgAuth` admin guard typed; audit writes use `InsertAccessAuditLog`

- Typing helpers
  - Introduced `server/utils/typing.ts` with `asInsert<T>`, `asUpdate<T>`, and `withUpdatedAt<T>()` to coerce Zod-validated payloads into Drizzle Insert*/Update* shapes and reduce `as any` at callsites.
  - Routes now wrap Zod parse outputs for RFQs, RFQ documents, bid line items, bid documents, forecast materials, and user feedback with `asInsert<...>(parsed)`.

- Settings page (frontend)
  - Contractor Profile extended to capture and persist:
    - `unionStatus`, `unionAffiliations`, `certifications[]` (MBE/WBE/DBE/SBE/VBE), `serviceAreas`
    - `keywordTags[]` and `preferredProjectTypes[]` (Residential, Commercial, Infrastructure, Industrial, Institutional, Education, Healthcare)
  - UI implemented in `client/src/pages/Settings.tsx`; constants in `client/src/constants/certifications.ts` and `client/src/constants/trades.ts`.
  - Persistence via `PUT /api/contractors/profile` → maps to `contractors` columns: `union_status`, `union_affiliations`, `certifications` (jsonb), `service_areas`, `keyword_tags` (jsonb), `preferred_project_types` (jsonb).
  - Clerk section simplified: embedded `<UserProfile />` removed; replaced with compact `<UserButton />` and `<OrganizationSwitcher />` only. No deep-link buttons rendered by default.
  - Env for future deep links (optional): `VITE_CLERK_ACCOUNT_PORTAL_URL` — base URL for Clerk Account Portal (e.g., `https://accounts.your-domain.com`).

- Current status
  - Green `npm run check` (server-only)
  - File-level suppressions removed and types aligned across:
    - `server/storage.ts`, `server/routes.ts`
    - `server/services/aiService.ts`, `server/services/auditService.ts`, `server/services/apiKeyService.ts`
    - `server/services/enhancedBidAnalytics.ts`, `server/services/enhancedBidComparison.ts`
    - `server/services/masterSummaryService.ts`, `server/services/perplexityService.ts`, `server/services/progressService.ts`
  - Notable adjustments: unified extractor error guard; progress stage typing; replaced `rfq.title` with `rfq.projectName`; cache usage normalized where needed.
  - Follow-ups: migrate remaining update payload casts in `storage.ts` to `asUpdate/withUpdatedAt`; tighten local interfaces for analytics where shared schema lacks types.

- How to run
  - Typecheck: `npm run check`
  - Dev: `npm run dev`
  - Build: `npm run build`

## Deployment Notes – Railway (August 2025)

- Dev URL: https://bidaible-dev.up.railway.app
- Required env on Railway (server): `DATABASE_URL`, `CLERK_SECRET_KEY`, `CLERK_PUBLISHABLE_KEY`, at least one AI key, Wasabi creds.
- Required env on Railway (client): `VITE_CLERK_PUBLISHABLE_KEY` (pk_…).
- Optional env: `APP_ORIGIN` or `WEB_APP_ORIGIN` to explicitly allow a specific origin via CORS.

### Runtime public env injection (prevents blank screen when VITE_ vars are missing at build time)
- Server injects a small script into built index.html that sets `window.__PUBLIC_ENV__` with safe public values.
  - See `server/vite.ts` → `serveStatic()` injection.
- Client reads `import.meta.env.VITE_CLERK_PUBLISHABLE_KEY` and falls back to `window.__PUBLIC_ENV__`.
  - See `client/src/App.tsx` near `ClerkProvider` initialization.

### CORS policy (server)
- Config in `server/middleware/security.ts` allows:
  - Explicit origins from env (`APP_ORIGIN`, `WEB_APP_ORIGIN`) and the project list.
  - Dynamic preview domains that match `*.up.railway.app`, plus common PaaS patterns (Vercel/Netlify/Render).
- Development is permissive. Production logs rejected origins.

### Clerk configuration
- Ensure the dev domain (e.g., `bidaible-dev.up.railway.app`) is permitted in Clerk (allowed origins/satellites).

### Verification / troubleshooting
- After deploy, hard refresh (disable cache) and confirm the page source contains `window.__PUBLIC_ENV__`.
- If you see "Not allowed by CORS" in server logs, set `APP_ORIGIN=https://<your-app>.up.railway.app` or rely on the dynamic pattern.
- If the app shows a configuration screen, set `VITE_CLERK_PUBLISHABLE_KEY` in Railway.

## Phase 0/1 Guardrails and Performance Updates (August 2025)

- Health endpoint added: GET `/api/health` returns `{ ok, uptimeMs, db, version }`
  - Verify: `curl http://localhost:5000/api/health`
- Compression: response compression enabled globally (see [index.ts](file:///c:/Users/<USER>/CascadeProjects/bidaible/Bidaible/server/index.ts))
- Logging: centralized leveled logger (pino) introduced (see [logger.ts](file:///c:/Users/<USER>/CascadeProjects/bidaible/Bidaible/server/utils/logger.ts))
  - Dev: pretty logs; Prod: concise method/path/status/duration without large JSON payloads
- File downloads: switched to streaming from Wasabi (avoid buffering large files)
  - Streaming helper: [objectStorageService.ts](file:///c:/Users/<USER>/CascadeProjects/bidaible/Bidaible/server/services/objectStorageService.ts#L215-L233)
  - File route streams data with proper headers (see [routes.ts](file:///c:/Users/<USER>/CascadeProjects/bidaible/Bidaible/server/routes.ts#L340-L414))
- Async filesystem I/O: replaced sync fs calls with async `fs.promises` in hot paths
  - Cost codes endpoints: [routes.ts](file:///c:/Users/<USER>/CascadeProjects/bidaible/Bidaible/server/routes.ts#L243-L260), [routes.ts](file:///c:/Users/<USER>/CascadeProjects/bidaible/Bidaible/server/routes.ts#L267-L305)
  - Attached assets streaming: [routes.ts](file:///c:/Users/<USER>/CascadeProjects/bidaible/Bidaible/server/routes.ts#L309-L338)
- Cross-platform temp files: use `os.tmpdir()` instead of hard-coded `/tmp` for temporary AI files (multiple callsites in [routes.ts](file:///c:/Users/<USER>/CascadeProjects/bidaible/Bidaible/server/routes.ts))
- Dependencies added: `compression`, `pino` (dev: `pino-pretty`). Run `npm install` after pulling.

Verification quick checks
- Health: `curl http://localhost:5000/api/health`
- Download/view a PDF served via `/api/files/:documentId?view=true` — should stream without high memory usage
- Cost codes: `curl http://localhost:5000/api/cost-codes | head -n 5`

---

**Last Updated**: August 26, 2025  
**Version**: 2.4 - RFQ Archive System Route Fix & UI Improvements  
**Contact**: Check project documentation for support channels
