import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useRoute } from "wouter";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import ReactMarkdown from "react-markdown";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  CheckCircle,
  XCircle,
  MessageSquare,
  Filter,
  ArrowUpDown,
  Eye,
  FileText,
  User,
  Clock,
  Award,
  AlertTriangle,
  Settings,
  Calculator,
  Search,
} from "lucide-react";
import { tradeOptions } from "@/constants/trades";
import { format, formatDistanceToNow } from "date-fns";

interface BidAnalysisData {
  rfq: any;
  bidSummary: {
    totalBids: number;
    acceptedBids: number;
    rejectedBids: number;
    pendingBids: number;
    baseTotal: number;
    bufferAmount: number;
    totalWithBuffer: number;
  };
  bids: Array<{
    bid: {
      id: string;
      bidAmount: number | null;
      extractedAmount: number | null;
      timeline: string | null;
      extractedTimeline: string | null;
      extractedScope: string | null;
      extractedConditions: string | null;
      status: string;
      submittedAt: string;
      reviewedAt: string | null;
      reviewNotes: string | null;
      aiSummary: string | null;
      competitiveScore: number | null;
      aiAnalysis: any;
      requestInfoMessage: string | null;
    };
    contractor: {
      id: string;
      companyName: string;
      primaryContactName: string;
      primaryContactEmail: string;
      primaryContactPhone: string;
      tradeTypes: any;
      yearsInBusiness: number | null;
      workforceSize: number | null;
    };
  }>;
}

export default function RfqBidManagement() {
  const [, params] = useRoute("/rfqs/:rfqId/bids");
  const rfqId = params?.rfqId;

  // Get URL search parameters for direct bid navigation
  const urlParams = new URLSearchParams(window.location.search);
  const autoOpenBidId = urlParams.get("bidId");

  const [sortBy, setSortBy] = useState("submittedAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [statusFilter, setStatusFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedBid, setSelectedBid] = useState<any>(null);
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [bufferDialogOpen, setBufferDialogOpen] = useState(false);
  const [bufferPercentage, setBufferPercentage] = useState("10.00");
  const [bufferNotes, setBufferNotes] = useState("");

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch bid analysis data
  const { data: analysisData, isLoading } = useQuery<BidAnalysisData>({
    queryKey: ["/api/rfqs", rfqId, "bids", "analysis"],
    enabled: !!rfqId,
  });

  // Set buffer data when loaded
  useEffect(() => {
    if (analysisData?.rfq) {
      setBufferPercentage(analysisData.rfq.bufferPercentage || "10.00");
      setBufferNotes(analysisData.rfq.bufferNotes || "");
    }
  }, [analysisData]);

  // Auto-open specific bid dialog when bidId is in URL
  useEffect(() => {
    if (autoOpenBidId && analysisData?.bids) {
      const targetBidData = analysisData.bids.find(({ bid }) => bid.id === autoOpenBidId);
      if (targetBidData) {
        setSelectedBid(targetBidData);
        setActionDialogOpen(true);
      }
    }
  }, [autoOpenBidId, analysisData?.bids]);

  // Bid action mutation
  const bidActionMutation = useMutation({
    mutationFn: async ({ bidId, action, notes, message }: any) => {
      return apiRequest("PATCH", `/api/bids/${bidId}`, {
        status: action,
        notes,
        message,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/rfqs", rfqId, "bids", "analysis"] });
      setActionDialogOpen(false);
      setSelectedBid(null);
      toast({ title: "Bid action completed successfully" });
    },
    onError: (error: any) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    },
  });

  // Buffer update mutation
  const bufferUpdateMutation = useMutation({
    mutationFn: async (data: { bufferPercentage: string; bufferNotes: string }) => {
      return apiRequest("PUT", `/api/rfqs/${rfqId}/buffer`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/rfqs", rfqId, "bids", "analysis"] });
      setBufferDialogOpen(false);
      toast({ title: "Buffer settings updated successfully" });
    },
    onError: (error: any) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    },
  });

  const sortedAndFilteredBids = analysisData?.bids
    ?.filter(({ bid, contractor }) => {
      // Status filter
      if (statusFilter !== "all") {
        if (statusFilter === "pending") {
          if (["Accepted", "Rejected"].includes(bid.status)) return false;
        } else {
          if (bid.status.toLowerCase() !== statusFilter.toLowerCase()) return false;
        }
      }

      // Search filter
      if (searchTerm.trim()) {
        const searchLower = searchTerm.toLowerCase();
        const contractorName = contractor.companyName?.toLowerCase() || "";
        const contactName = contractor.primaryContactName?.toLowerCase() || "";
        const bidAmount = (bid.bidAmount || bid.extractedAmount || "").toString().toLowerCase();
        const timeline = (bid.timeline || bid.extractedTimeline || "").toLowerCase();

        return (
          contractorName.includes(searchLower) ||
          contactName.includes(searchLower) ||
          bidAmount.includes(searchLower) ||
          timeline.includes(searchLower)
        );
      }

      return true;
    })
    ?.sort((a, b) => {
      const aValue = getSortValue(a, sortBy);
      const bValue = getSortValue(b, sortBy);

      if (sortOrder === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

  function getSortValue(item: any, sortBy: string) {
    switch (sortBy) {
      case "submittedAt":
        return new Date(item.bid.submittedAt).getTime();
      case "contractorName":
        return item.contractor.companyName.toLowerCase();
      case "bidAmount":
        return Number(item.bid.bidAmount || item.bid.extractedAmount || 0);
      case "contractorCategory":
        return getPrimaryTradeType(item.contractor.tradeTypes);
      default:
        return 0;
    }
  }

  function formatStatus(status: string) {
    switch (status.toLowerCase()) {
      case "accept":
        return "Accepted";
      case "reject":
        return "Rejected";
      case "request info":
        return "Request Info";
      case "pending":
        return "Pending";
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  }

  function getStatusColor(status: string) {
    const normalizedStatus = status.toLowerCase();
    switch (normalizedStatus) {
      case "accepted":
      case "accept":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "rejected":
      case "reject":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "request info":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      default:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
    }
  }

  function formatCurrency(amount: number | null) {
    if (!amount) return "TBD";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(amount);
  }

  function getPrimaryTradeType(tradeTypes: any): string {
    if (!tradeTypes || tradeTypes.length === 0) return "No category";
    const primaryTrade = Array.isArray(tradeTypes) ? tradeTypes[0] : tradeTypes;
    const tradeOption = tradeOptions.find((option) => option.value === primaryTrade);
    return tradeOption?.label || "Unknown";
  }

  function formatTradeTypes(tradeTypes: any): string {
    if (!tradeTypes || tradeTypes.length === 0) return "No category";

    const trades = Array.isArray(tradeTypes) ? tradeTypes : [tradeTypes];
    const primaryTrade = tradeOptions.find((option) => option.value === trades[0]);
    const primaryLabel = primaryTrade?.label || "Unknown";

    if (trades.length === 1) {
      return primaryLabel;
    } else {
      return `${primaryLabel} +${trades.length - 1}`;
    }
  }

  function handleBidAction(bid: any, action: string, notes?: string, message?: string) {
    bidActionMutation.mutate({
      bidId: bid.id,
      action,
      notes: notes || "",
      message: message || "",
    });
    // Close the dialog after action
    setActionDialogOpen(false);
    setSelectedBid(null);

    // Clean up URL parameter after action
    if (autoOpenBidId) {
      const url = new URL(window.location.href);
      url.searchParams.delete("bidId");
      window.history.replaceState({}, "", url);
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-96" />
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-muted rounded-lg" />
            ))}
          </div>
          <div className="h-96 bg-muted rounded-lg" />
        </div>
      </div>
    );
  }

  if (!analysisData) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold mb-2">RFQ Not Found</h2>
          <p className="text-muted-foreground">
            The requested RFQ could not be found or you don't have access.
          </p>
        </div>
      </div>
    );
  }

  const { rfq, bidSummary, bids } = analysisData;

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Bid Management Dashboard</h1>
          <p className="text-lg text-muted-foreground">{rfq.projectName}</p>
          <p className="text-sm text-muted-foreground">
            {rfq.projectLocation} • Due: {format(new Date(rfq.dueDate), "MMM dd, yyyy")}
          </p>
        </div>
        <Dialog open={bufferDialogOpen} onOpenChange={setBufferDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Buffer Settings
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Manage Cost Buffer</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="bufferPercentage">Buffer Percentage (%)</Label>
                <Input
                  id="bufferPercentage"
                  type="number"
                  step="0.01"
                  value={bufferPercentage}
                  onChange={(e) => setBufferPercentage(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="bufferNotes">Buffer Notes</Label>
                <Textarea
                  id="bufferNotes"
                  value={bufferNotes}
                  onChange={(e) => setBufferNotes(e.target.value)}
                  placeholder="Explain the reasoning for this buffer percentage..."
                />
              </div>
              <Button
                onClick={() => bufferUpdateMutation.mutate({ bufferPercentage, bufferNotes })}
                disabled={bufferUpdateMutation.isPending}
              >
                {bufferUpdateMutation.isPending ? "Updating..." : "Update Buffer"}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Bids</p>
                <p className="text-2xl font-bold">{bidSummary.totalBids}</p>
              </div>
              <FileText className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Accepted Bids</p>
                <p className="text-2xl font-bold text-green-600">{bidSummary.acceptedBids}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Base Total</p>
                <p className="text-2xl font-bold">{formatCurrency(bidSummary.baseTotal)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total + Buffer</p>
                <p className="text-2xl font-bold text-orange-600">
                  {formatCurrency(bidSummary.totalWithBuffer)}
                </p>
                <p className="text-xs text-muted-foreground">
                  +{formatCurrency(bidSummary.bufferAmount)} ({bufferPercentage}%)
                </p>
              </div>
              <Calculator className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Sorting */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              <Label>Search:</Label>
              <Input
                placeholder="Search contractors, amounts, timeline..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-80"
              />
            </div>

            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              <Label>Filter by Status:</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Bids</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="accepted">Accepted</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="request info">Request Info</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <ArrowUpDown className="h-4 w-4" />
              <Label>Sort by:</Label>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="submittedAt">Submission Date</SelectItem>
                  <SelectItem value="contractorName">Contractor Name</SelectItem>
                  <SelectItem value="contractorCategory">Contractor Category</SelectItem>
                  <SelectItem value="bidAmount">Bid Amount</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
              >
                {sortOrder === "asc" ? "↑" : "↓"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bids Table */}
      <Card>
        <CardHeader>
          <CardTitle>Bid Submissions ({sortedAndFilteredBids?.length || 0})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Contractor</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Bid Amount</TableHead>
                <TableHead>Timeline</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedAndFilteredBids?.map(({ bid, contractor }) => (
                <TableRow key={bid.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{contractor.companyName}</div>
                      <div className="text-sm text-muted-foreground">
                        {contractor.primaryContactName}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{formatTradeTypes(contractor.tradeTypes)}</div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">
                      {formatCurrency(Number(bid.bidAmount || bid.extractedAmount))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {bid.timeline || bid.extractedTimeline || "Not specified"}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(bid.status)}>{formatStatus(bid.status)}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {bid.submittedAt
                        ? formatDistanceToNow(new Date(bid.submittedAt), { addSuffix: true })
                        : "—"}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Dialog
                        open={actionDialogOpen && selectedBid?.bid.id === bid.id}
                        onOpenChange={(open) => {
                          setActionDialogOpen(open);
                          // Clean up URL parameter when dialog is manually closed
                          if (!open && autoOpenBidId) {
                            const url = new URL(window.location.href);
                            url.searchParams.delete("bidId");
                            window.history.replaceState({}, "", url);
                          }
                        }}
                      >
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedBid({ bid, contractor });
                              setActionDialogOpen(true);
                            }}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            Review
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle>Bid Review - {contractor.companyName}</DialogTitle>
                          </DialogHeader>
                          {selectedBid && (
                            <BidReviewDialog
                              bid={selectedBid.bid}
                              contractor={selectedBid.contractor}
                              onAction={handleBidAction}
                              isLoading={bidActionMutation.isPending}
                            />
                          )}
                        </DialogContent>
                      </Dialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {sortedAndFilteredBids?.length === 0 && (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No bids found</h3>
              <p className="text-muted-foreground">
                {statusFilter === "all"
                  ? "No bids have been submitted for this RFQ yet."
                  : `No bids with status "${statusFilter}" found.`}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Bid Review Dialog Component
function BidReviewDialog({
  bid,
  contractor,
  onAction,
  isLoading,
}: {
  bid: any;
  contractor: any;
  onAction: (bid: any, action: string, notes?: string, message?: string) => void;
  isLoading: boolean;
}) {
  const [reviewNotes, setReviewNotes] = useState("");
  const [requestInfoMessage, setRequestInfoMessage] = useState("");

  return (
    <div className="space-y-6">
      {/* Bid Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Bid Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <Label className="text-sm text-muted-foreground">Bid Amount</Label>
                <p className="text-2xl font-bold text-green-600">
                  {new Intl.NumberFormat("en-US", {
                    style: "currency",
                    currency: "USD",
                    maximumFractionDigits: 0,
                  }).format(Number(bid.bidAmount || bid.extractedAmount || 0))}
                </p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">Timeline</Label>
                <p>{bid.timeline || bid.extractedTimeline || "Not specified"}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">Competitive Score</Label>
                <p className="text-lg font-semibold">
                  {bid.competitiveScore
                    ? `${Math.round(bid.competitiveScore * 100)}%`
                    : "Not scored"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Contractor Profile
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <Label className="text-sm text-muted-foreground">Company</Label>
                <p className="font-medium">{contractor.companyName}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">Contact</Label>
                <p>{contractor.primaryContactName}</p>
                <p className="text-sm text-muted-foreground">{contractor.primaryContactEmail}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">Experience</Label>
                <p>
                  {contractor.yearsInBusiness
                    ? `${contractor.yearsInBusiness} years`
                    : "Not specified"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI Analysis */}
      {bid.aiAnalysis && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              AI Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {bid.aiSummary && (
                <div>
                  <Label className="text-sm text-muted-foreground">AI Summary</Label>
                  <div className="mt-2 prose prose-sm max-w-none dark:prose-invert">
                    <ReactMarkdown>{bid.aiSummary}</ReactMarkdown>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">Strengths</Label>
                  <ul className="text-sm list-disc list-inside space-y-1">
                    {bid.aiAnalysis.strengths?.map((strength: string, index: number) => (
                      <li key={index}>{strength}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <Label className="text-sm text-muted-foreground">Concerns</Label>
                  <ul className="text-sm list-disc list-inside space-y-1">
                    {bid.aiAnalysis.concerns?.map((concern: string, index: number) => (
                      <li key={index}>{concern}</li>
                    ))}
                  </ul>
                </div>
              </div>

              <div>
                <Label className="text-sm text-muted-foreground">Recommendations</Label>
                <ul className="text-sm list-disc list-inside space-y-1">
                  {bid.aiAnalysis.recommendations?.map((rec: string, index: number) => (
                    <li key={index}>{rec}</li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bid Content */}
      <Card>
        <CardHeader>
          <CardTitle>Bid Package Details</CardTitle>
        </CardHeader>
        <CardContent>
          <BidPackageContent bid={bid} />
        </CardContent>
      </Card>

      {/* Review Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Review Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label htmlFor="reviewNotes">Review Notes (Optional)</Label>
              <Textarea
                id="reviewNotes"
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                placeholder="Add notes about your decision..."
              />
            </div>

            <div className="flex gap-3">
              <Button
                onClick={() => onAction(bid, "accept", reviewNotes)}
                disabled={isLoading}
                className="bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Accept Bid
              </Button>

              <Button
                onClick={() => onAction(bid, "reject", reviewNotes)}
                disabled={isLoading}
                variant="destructive"
              >
                <XCircle className="h-4 w-4 mr-2" />
                Reject Bid
              </Button>

              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" disabled={isLoading}>
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Request Info
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Request Additional Information</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="requestMessage">Message to Contractor</Label>
                      <Textarea
                        id="requestMessage"
                        value={requestInfoMessage}
                        onChange={(e) => setRequestInfoMessage(e.target.value)}
                        placeholder="What additional information do you need?"
                      />
                    </div>
                    <Button
                      onClick={() => onAction(bid, "request_info", reviewNotes, requestInfoMessage)}
                      disabled={isLoading || !requestInfoMessage.trim()}
                    >
                      Send Request
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Bid Package Content Component
function BidPackageContent({ bid }: { bid: any }) {
  const { data: bidDocuments, isLoading } = useQuery({
    queryKey: ["/api/bids", bid.id, "documents"],
    enabled: !!bid.id,
  });

  if (isLoading) {
    return (
      <div className="animate-pulse space-y-3">
        <div className="h-4 bg-muted rounded w-32" />
        <div className="h-24 bg-muted rounded" />
        <div className="h-4 bg-muted rounded w-48" />
        <div className="h-32 bg-muted rounded" />
      </div>
    );
  }

  // Format bid content for display
  const formatBidContent = () => {
    let content = "";

    // Add AI summary first if available
    if (bid.aiSummary) {
      content += `${bid.aiSummary}\n\n`;
    }

    // Add proposal text if available
    if (bid.proposalText) {
      content += `## Proposal\n\n${bid.proposalText}\n\n`;
    }

    // Add extracted scope
    if (bid.extractedScope) {
      content += `## Scope of Work\n\n${bid.extractedScope}\n\n`;
    }

    // Add extracted conditions
    if (bid.extractedConditions) {
      content += `## Terms & Conditions\n\n${bid.extractedConditions}\n\n`;
    }

    // Add extracted data if available
    if (bid.extractedData) {
      try {
        const data =
          typeof bid.extractedData === "string" ? JSON.parse(bid.extractedData) : bid.extractedData;

        if (data.scope) {
          content += `## Project Scope\n\n${data.scope}\n\n`;
        }

        if (data.conditions) {
          content += `## Special Conditions\n\n${data.conditions}\n\n`;
        }

        if (data.materialCosts && data.materialCosts.length > 0) {
          content += `## Material Costs\n\n`;
          data.materialCosts.forEach((item: any) => {
            content += `- **${item.item}**: ${item.cost}\n`;
          });
          content += `\n`;
        }

        if (data.laborCosts && data.laborCosts.length > 0) {
          content += `## Labor Costs\n\n`;
          data.laborCosts.forEach((item: any) => {
            content += `- **${item.category}**: ${item.cost}\n`;
          });
          content += `\n`;
        }

        if (data.schedule) {
          content += `## Project Schedule\n\n${data.schedule}\n\n`;
        }

        if (data.insurance) {
          content += `## Insurance Information\n\n${data.insurance}\n\n`;
        }

        if (data.bondingRequired) {
          content += `## Bonding\n\n${data.bondingRequired ? "Bonding required and available" : "No bonding required"}\n\n`;
        }

        if (data.warranty) {
          content += `## Warranty\n\n${data.warranty}\n\n`;
        }

        if (data.exclusions) {
          content += `## Exclusions\n\n${data.exclusions}\n\n`;
        }
      } catch (error) {
        console.error("Error parsing extracted data:", error);
      }
    }

    // Add document texts if available
    if (bidDocuments && bidDocuments.length > 0) {
      bidDocuments.forEach((doc: any) => {
        if (doc.extractedText) {
          content += `## Document: ${doc.fileName}\n\n${doc.extractedText}\n\n`;
        }
      });
    }

    return content || "No bid package details available.";
  };

  const markdownContent = formatBidContent();

  return (
    <div className="space-y-4">
      <div className="prose prose-sm max-w-none dark:prose-invert">
        <ReactMarkdown>{markdownContent}</ReactMarkdown>
      </div>

      {/* Show document count if available */}
      {bidDocuments && bidDocuments.length > 0 && (
        <div className="mt-4 p-3 bg-muted rounded-md">
          <p className="text-sm text-muted-foreground">
            📄 {bidDocuments.length} document{bidDocuments.length > 1 ? "s" : ""} analyzed
          </p>
        </div>
      )}
    </div>
  );
}
