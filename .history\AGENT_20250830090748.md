# Bidaible Development Guide

## Project Overview

Bidaible is an AI-powered construction bidding platform built with enterprise-grade TypeScript architecture. The system delivers advanced bid analysis using SOTA LLMs.

**Version**: 2.4  
**Last Updated**: August 26, 2025

## Quick Start

### Prerequisites
- Node.js (with ES modules support)
- PostgreSQL database
- At least one AI API key (Groq, OpenAI, or Gemini)
- Wasabi S3-compatible storage credentials
- Clerk authentication keys

### Initial Setup
1. `npm install` - Install dependencies
2. Copy `.env.example` to `.env` and configure (see Environment Variables section)
3. `npm run db:push` - Initialize database schema
4. `npm run dev` - Start development server

## Tech Stack

### Frontend
- **Framework**: React 18 + TypeScript
- **Build**: Vite with HMR
- **Routing**: Wouter (lightweight client-side)
- **Styling**: Tailwind CSS + shadcn/ui
- **State Management**: TanStack Query (server) + React Context (global)
- **Forms**: React Hook Form + Zod validation
- **Notifications**: React-Toastify

### Backend
- **Runtime**: Node.js with ES modules
- **Server**: Express + TypeScript
- **Database**: PostgreSQL + Drizzle ORM (30+ strategic indexes)
- **Authentication**: Clerk Auth + JWT API keys with RBAC
- **File Storage**: Wasabi S3-compatible
- **AI Processing**: Multi-provider (Groq → OpenAI → Gemini fallback)
- **Caching**: In-memory TTL/LRU + database indexes

## Development Commands

### Core Commands
| Command | Description | Port |
|---------|-------------|------|
| `npm run dev` | Full stack development (API + Vite) | 5000 |
| `npm run dev:api` | API-only mode for testing | 5000 |
| `npm run build` | Production build | - |
| `npm start` | Production server | 5000 |
| `npm run check` | TypeScript checking (server-only) | - |
| `npm run db:push` | Apply database schema changes | - |

### Testing Commands
```bash
# Main test suite
cd tests && npm test

# Specific test suites
cd tests && npm run test:wasabi    # Storage tests
cd tests && npm run test:ai        # AI processing tests

# Security tests (requires API-only mode)
npm run dev:api                    # Terminal 1
node tests/test-rfq-update-access.js  # Terminal 2
node tests/test-docs-files-access.js
node tests/test-rate-limits.js
node tests/test-admin-routes.js
```

## Project Structure

```
bidaible/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/    # UI components
│   │   ├── pages/        # Route pages
│   │   ├── constants/    # Shared constants (trades, certifications)
│   │   └── utils/        # Utilities (countdown, API)
├── server/                 # Express backend
│   ├── routes.ts          # API endpoints
│   ├── services/          # Business logic
│   ├── storage.ts         # Database layer
│   ├── schema/            # Drizzle schemas
│   ├── middleware/        # Auth, security, rate limiting
│   └── utils/            # Helpers (typing, logger)
├── shared/                # Shared types
├── tests/                 # Test suites
├── docs/                  # Documentation
└── migrations/            # Database migrations
```

## Environment Variables

### Required Variables

```env
# Database
DATABASE_URL=postgresql://user:pass@host:port/db
PGDATABASE=
PGHOST=
PGPORT=
PGUSER=
PGPASSWORD=

# AI Services (at least one required)
GROQ_API_KEY=gsk_...
OPENAI_API_KEY=sk-...
GEMINI_API_KEY=...
PRIMARY_MODEL=groq  # Options: groq, openai, gemini

# Authentication
CLERK_SECRET_KEY=sk_test_...
CLERK_PUBLISHABLE_KEY=pk_test_...

# File Storage
WASABI_ACCESS_KEY_ID=...
WASABI_SECRET_ACCESS_KEY=...
WASABI_BUCKET_NAME=bidaible-storage
WASABI_ENDPOINT=https://s3.wasabisys.com

# Application
NODE_ENV=development
SESSION_SECRET=...
JWT_SECRET=...
```
# Optional: For development
PORT=5000

#Sentry Monitoring
SENTRY_DSN= 
VITE_SENTRY_DSN=

# Github Issues 
GITHUB_TOKEN=7
GITHUB_REPO_OWNER= 
GITHUB_REPO_NAME=
GITHUB_ASSIGNEE=

### Frontend-Specific
- `VITE_CLERK_PUBLISHABLE_KEY` - Clerk public key (required for production builds)
- `VITE_CLERK_ACCOUNT_PORTAL_URL` - Optional account portal URL

## Key Features

### AI Processing Pipeline
```
Upload → Validation → Text Extraction → AI Analysis → Data Mapping → Storage
```
- **Document Support**: PDF, TXT, CSV (95%+ success rate)
- **Multi-Provider Fallback**: Groq (speed) → OpenAI (reliability) → Gemini (vision/OCR)
- **Processing Time**: Sub-3 second analysis generation
- **File Limits**: 50MB per file, 8 files per RFQ

### RFQ Deadline Management
- **Dual Deadlines**: Bid proposal deadline + project deadline
- **Automated Reminders**: 1 week, 3 days, 1 day notifications
- **Background Processor**: Runs every minute via `ScheduledNotificationProcessor`
- **Real-time Countdown**: Color-coded urgency (green/yellow/red/gray)
- **Database Validation**: Constraint ensures bid deadline < project deadline

### Notification System
- **Real-time Updates**: 30-60 second polling via TanStack Query
- **Notification Types**: Deadline reminders, bid submissions, status changes
- **Priority Levels**: Urgent/High/Medium/Low with visual indicators
- **Interactive UI**: Bell icon with unread count, click-to-navigate dropdown

### Multi-Tenant Architecture
- **Organization Isolation**: Complete data separation via Clerk
- **File Storage**: Organization-scoped with path structure:
  ```
  rfq-documents/org-{orgId}/rfq-{rfqId}/original/
  bids/contractor-{name}/
  ```
- **RBAC**: Admin, Editor, Viewer roles
- **Audit Logging**: Comprehensive activity tracking

### RFQ Archive System
- **Archive Management**: Archive/unarchive RFQs with bulk operations
- **ZIP Downloads**: Complete project bundles (RFQ docs + submitted bids)
- **Access Control**: Only RFQ owner's organization can access archives
- **Contractor Protection**: Archived RFQs hidden from contractors
- **API Endpoints**:
  - `PATCH /api/rfqs/:id/archive` - Archive single
  - `PATCH /api/rfqs/bulk-archive` - Archive multiple
  - `GET /api/rfqs/archived` - List archived
  - `GET /api/rfqs/:id/archive-download` - Download ZIP

## API Structure

```
/api/auth/*          # Authentication
/api/rfqs/*          # RFQ management
/api/contractors/*   # Contractor profiles
/api/bids/*          # Bid management
/api/dashboard/*     # Statistics
/api/files/*         # Document handling
/api/notifications/* # Notification management
```

## Security

### Authentication Flow
- **Web App**: Clerk session cookies with `credentials: "include"`
- **API Access**: JWT tokens for programmatic access
- **User Classification**: Auto-sync based on trade types:
  - "General Contractor" trade → `userClassification: "general_contractor"`
  - Other trades → `userClassification: "contractor"`

### Data Protection
- **Input Validation**: Zod schemas on all endpoints
- **SQL Injection Prevention**: Parameterized queries via Drizzle ORM
- **File Security**: Type validation, size limits, secure storage
- **Rate Limiting**: Per-user and per-API-key throttling
- **CORS Policy**: Dynamic Railway preview domains + explicit origins

## Performance Optimizations

### Recent Improvements (August 2025)
- **Health Endpoint**: `GET /api/health` for monitoring
- **Response Compression**: Enabled globally
- **Streaming Downloads**: Large files stream from Wasabi
- **Async I/O**: Replaced sync filesystem calls
- **Logging**: Centralized with Pino (structured logging)
- **Cross-platform**: Uses `os.tmpdir()` for temp files

### Target Metrics
- **Concurrent Users**: Designed for 10,000+
- **AI Processing**: < 3 seconds
- **Document Success Rate**: 95%+
- **Memory Management**: Streaming for large files

## Development Workflow

### Database Management
- **Schema Changes**: Edit `server/schema/*` then run `npm run db:push`
- **Migrations**: Located in `migrations/` directory
- **Indexes**: 30+ strategic indexes pre-configured

### TypeScript Configuration
- **Strict Mode**: Enabled for type safety
- **Server Config**: `tsconfig.server.json` for focused checking
- **Typing Helpers**: `asInsert<T>`, `asUpdate<T>`, `withUpdatedAt<T>` in `server/utils/typing.ts`

### Testing Strategy
- **Dev Fixtures**: `/api/test/*` endpoints (development only)
- **Security Tests**: Require API-only mode (`npm run dev:api`)
- **Test Database**: Use `TEST_DATABASE_URL` for CI

## Deployment (Railway)

### Configuration
- **Dev URL**: https://bidaible-dev.up.railway.app
- **Required Env**: All server env vars + `VITE_CLERK_PUBLISHABLE_KEY`
- **Runtime Injection**: Server injects `window.__PUBLIC_ENV__` for client config
- **CORS**: Automatic Railway preview domain support

### Verification
```bash
# Check health
curl https://your-app.up.railway.app/api/health

# Verify public env injection
# View page source, look for window.__PUBLIC_ENV__
```

## Common Issues & Solutions

| Issue | Solution |
|-------|----------|
| Build errors | Run `npm run check` for TypeScript validation |
| Database issues | Verify DATABASE_URL and run `npm run db:push` |
| RFQs not showing | Check organizationId exists in database |
| File upload fails | Verify Wasabi credentials and bucket access |
| Auth issues | Ensure Clerk keys match environment |
| Documents not visible | Check session cookies are sent (`credentials: "include"`) |

## Critical Files - DO NOT MODIFY WITHOUT TESTING

- `server/services/aiOptimizedService.ts` - Core AI processing
- `server/services/core/pdfExtractor.ts` - PDF extraction pipeline
- AI data flow in `server/routes.ts`

## Debugging Tools

```bash
# Test API connectivity
curl http://localhost:5000/api/health

# Test file upload (development)
curl -X POST http://localhost:5000/api/test/rfqs \
  -F "documents=@test.pdf" \
  -F "projectName=Test"

# Check environment (if debug script exists)
node debug-user-context.cjs
```

## Support & Documentation

- **Technical Docs**: `/docs/` directory
- **Testing Guide**: `/tests/README.md`
- **Deployment**: `/DEPLOYMENT.md`
- **AI Safeguards**: `/docs/AI_PROCESSING_SAFEGUARDS.md`

---
