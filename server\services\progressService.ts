/**
 * Progress Tracking Service
 * Manages real-time upload and processing progress using Server-Sent Events
 */

import type { UploadProgress } from './objectStorageService';
import { UploadStage, UploadStatus } from '../../shared/schema';

interface ProgressSession {
  sessionId: string;
  userId: string;
  files: Map<string, UploadProgress>;
  response?: any; // SSE response object
}

class ProgressTracker {
  private sessions = new Map<string, ProgressSession>();

  /**
   * Create a new progress tracking session
   */
  createSession(sessionId: string, userId: string): void {
    this.sessions.set(sessionId, {
      sessionId,
      userId,
      files: new Map(),
    });
  }

  /**
   * Set up Server-Sent Events for a session
   */
  setupSSE(sessionId: string, response: any): void {
    console.log(`🚨 SSE SETUP: Client connecting to session ${sessionId}`);
    
    let session = this.sessions.get(sessionId);
    if (!session) {
      // Create session if it doesn't exist
      console.log(`🚨 SSE SETUP: Creating missing session ${sessionId}`);
      this.createSession(sessionId, 'anonymous');
      session = this.sessions.get(sessionId);
      if (!session) {
        throw new Error('Failed to create session');
      }
    } else {
      console.log(`🚨 SSE SETUP: Found existing session ${sessionId}`);
    }

    // Set SSE headers
    response.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    session.response = response;

    // Send initial connection message
    this.sendSSE(sessionId, 'connected', { sessionId });

    // Handle client disconnect
    response.on('close', () => {
      this.endSession(sessionId);
    });
  }

  /**
   * Update progress for a file in a session with enhanced stage tracking
   */
  updateProgress(sessionId: string, progress: UploadProgress): void {
    console.log('🚨 SERVER Progress Update:', {
      sessionId,
      fileName: progress.fileName,
      percentage: progress.percentage,
      stage: progress.stage,
      status: progress.status
    });
    
    const session = this.sessions.get(sessionId);
    if (!session) {
      console.warn(`Session ${sessionId} not found for progress update`);
      return;
    }

    // Add file size context for large files
    const fileSizeMB = progress.totalBytes / (1024 * 1024);
    const enhancedProgress = {
      ...progress,
      fileSizeMB: Math.round(fileSizeMB * 10) / 10,
      isLargeFile: fileSizeMB > 100,
      estimatedTimeRemaining: this.calculateETA(progress)
    };

    session.files.set(progress.fileName, enhancedProgress);
    this.sendSSE(sessionId, 'progress', enhancedProgress);
  }

  /**
   * Calculate estimated time remaining for large file processing
   */
  private calculateETA(progress: UploadProgress): string {
    const fileSizeMB = progress.totalBytes / (1024 * 1024);
    const remainingPercentage = 100 - progress.percentage;
    
    // Enhanced ETA calculation for different stages
    let estimatedSeconds = 0;
    const stage = (progress as any).stage;
    
    // Handle both old and new stage formats for backward compatibility
    if (stage === UploadStage.UPLOAD_START || stage === UploadStage.UPLOAD_IN_PROGRESS || 
        stage === 'upload_chunked' || stage === 'upload_start' || stage === 'upload') {
      estimatedSeconds = (fileSizeMB * remainingPercentage / 100) / 5 * 60;
    } else if (stage === UploadStage.TEXT_EXTRACTION || stage === 'text_extraction') {
      estimatedSeconds = (fileSizeMB / 50) * 30 * (remainingPercentage / 100);
    } else if (stage === UploadStage.AI_PROCESSING || stage === 'ai_processing') {
      // AI processing: ~60 seconds regardless of file size
      estimatedSeconds = 60 * (remainingPercentage / 100);
    } else {
      estimatedSeconds = 10 * (remainingPercentage / 100);
    }

    if (estimatedSeconds < 60) {
      return `${Math.round(estimatedSeconds)} seconds`;
    } else {
      const minutes = Math.round(estimatedSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''}`;
    }
  }

  /**
   * Send Server-Sent Event to a session
   */
  private sendSSE(sessionId: string, event: string, data: any): void {
    const session = this.sessions.get(sessionId);
    if (!session?.response) return;

    try {
      session.response.write(`event: ${event}\n`);
      session.response.write(`data: ${JSON.stringify(data)}\n\n`);
    } catch (error) {
      console.error('Error sending SSE:', error);
      this.endSession(sessionId);
    }
  }

  /**
   * Get current progress for all files in a session
   */
  getSessionProgress(sessionId: string): UploadProgress[] {
    const session = this.sessions.get(sessionId);
    if (!session) return [];

    return Array.from(session.files.values());
  }

  /**
   * Complete a file upload
   */
  completeFile(sessionId: string, fileName: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const progress = session.files.get(fileName);
    if (progress) {
      progress.status = UploadStatus.COMPLETE as any;
      progress.stage = UploadStage.COMPLETE as any;
      progress.percentage = 100;
      session.files.set(fileName, progress);
      this.sendSSE(sessionId, 'complete', progress);
    }
  }

  /**
   * Mark a file as failed
   */
  failFile(sessionId: string, fileName: string, error: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const progress = session.files.get(fileName);
    if (progress) {
      progress.status = UploadStatus.ERROR as any;
      session.files.set(fileName, progress);
      this.sendSSE(sessionId, 'error', { ...progress, error });
    }
  }

  /**
   * End a session and clean up
   */
  endSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session?.response) {
      try {
        session.response.end();
      } catch (error) {
        console.error('Error ending SSE response:', error);
      }
    }
    this.sessions.delete(sessionId);
  }

  /**
   * Clean up expired sessions (older than 1 hour)
   */
  cleanupExpiredSessions(): void {
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    
    for (const [sessionId, session] of Array.from(this.sessions.entries())) {
      const sessionAge = Date.now() - parseInt(sessionId);
      if (sessionAge > oneHourAgo) {
        this.endSession(sessionId);
      }
    }
  }
}

export const progressTracker = new ProgressTracker();

// Clean up expired sessions every 15 minutes
setInterval(() => {
  progressTracker.cleanupExpiredSessions();
}, 15 * 60 * 1000);