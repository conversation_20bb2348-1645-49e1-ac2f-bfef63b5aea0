# Bidaible Application - Complete User Journey & System Flow

This document provides a comprehensive overview of the complete user journey and system flow in the Bidaible application, from user authentication through file archival.

## Mermaid Flowchart Diagram

```mermaid
flowchart TD
    %% Authentication Flow
    subgraph AUTH["🔐 Authentication System"]
        A1[User Visits Application] --> A2{Authenticated?}
        A2 -->|No| A3[Landing Page]
        A3 --> A4[Clerk Sign-In Component]
        A4 --> A5[Clerk OAuth Flow]
        A5 --> A6{Auth Success?}
        A6 -->|No| A7[Auth Error]
        A6 -->|Yes| A8[JWT Token Issued]
        A8 --> A9[Session Established]
        A9 --> A10[Create/Update User in DB]
        A10 --> A11[Redirect to Dashboard]
        A2 -->|Yes| A11
    end

    %% Main Application Flow
    subgraph MAIN["📊 Main Application"]
        A11 --> M1[Dashboard Loaded]
        M1 --> M2{User Action}
        M2 --> M3[Create RFQ]
        M2 --> M4[View RFQs]
        M2 --> M5[Manage Contractors]
        M2 --> M6[View Bids]
        M2 --> M7[Analytics]
    end

    %% File Upload Flow
    subgraph UPLOAD["📁 File Upload System"]
        M3 --> U1[RFQ Form Component]
        U1 --> U2[MultiFileUploadInterface]
        U2 --> U3[File Selection/Drop]
        U3 --> U4[File Validation]
        U4 --> U5{Valid Files?}
        U5 -->|No| U6[Validation Error]
        U5 -->|Yes| U7[Progress Session Init]
        U7 --> U8[Multer Middleware]
        U8 --> U9{File Size Check}
        U9 -->|>10MB| U10[Disk Storage]
        U9 -->|<10MB| U11[Memory Storage]
        U10 --> U12[File Processing Middleware]
        U11 --> U12
        U12 --> U13[Checksum Calculation]
        U13 --> U14[Object Storage Upload]
    end

    %% Object Storage Flow
    subgraph STORAGE["☁️ Object Storage (Wasabi S3)"]
        U14 --> S1[Generate Object Key]
        S1 --> S2[org-{orgId}/{rfqId}/{timestamp}-{filename}]
        S2 --> S3{Large File?}
        S3 -->|Yes| S4[Chunked Upload]
        S3 -->|No| S5[Standard Upload]
        S4 --> S6[Progress Tracking]
        S5 --> S6
        S6 --> S7[File Stored Successfully]
        S7 --> S8[Return Storage Metadata]
    end

    %% Processing Flow
    subgraph PROCESS["🤖 AI Processing Engine"]
        S8 --> P1[Create DB Record]
        P1 --> P2[Initialize Progress Tracking]
        P2 --> P3[UnifiedPDFExtractor]
        P3 --> P4{File Type?}
        P4 -->|PDF| P5[PDF.js Extraction]
        P4 -->|TXT/CSV| P6[Stream Text Processor]
        P5 --> P7{Extraction Success?}
        P7 -->|No| P8[pdf-parse Fallback]
        P8 --> P9{Fallback Success?}
        P9 -->|No| P10[Gemini Vision OCR]
        P9 -->|Yes| P11[Text Extracted]
        P10 --> P11
        P7 -->|Yes| P11
        P6 --> P11
        P11 --> P12[AI Processing Engine]
    end

    %% AI Processing Details
    subgraph AI["🧠 AI Analysis"]
        P12 --> AI1{Primary AI Model}
        AI1 -->|Groq| AI2[Groq API Processing]
        AI1 -->|OpenAI| AI3[OpenAI API Processing]
        AI1 -->|Gemini| AI4[Gemini Processing]
        AI2 --> AI5[Structured Data Extraction]
        AI3 --> AI5
        AI4 --> AI5
        AI5 --> AI6[Data Validation]
        AI6 --> AI7[Schema Mapping]
        AI7 --> AI8[AI Summary Generation]
        AI8 --> AI9[Processing Complete]
    end

    %% Database Storage
    subgraph DB["🗄️ Database Storage"]
        AI9 --> DB1[Update RFQ Record]
        DB1 --> DB2[Store Extracted Data]
        DB2 --> DB3[Create Document Records]
        DB3 --> DB4[Update Processing Status]
        DB4 --> DB5[Audit Log Creation]
        DB5 --> DB6[Progress Update Complete]
    end

    %% Real-time Updates
    subgraph REALTIME["⚡ Real-time Updates"]
        DB6 --> RT1[Server-Sent Events]
        RT1 --> RT2[Progress Tracker Service]
        RT2 --> RT3[Client Progress Updates]
        RT3 --> RT4[UI State Updates]
        RT4 --> RT5[Processing Complete Notification]
    end

    %% Bid Management Flow
    subgraph BIDS["💼 Bid Management"]
        M6 --> B1[Bid Submission Form]
        B1 --> B2[Bid Document Upload]
        B2 --> B3[Similar Upload Flow]
        B3 --> B4[Bid AI Processing]
        B4 --> B5[Cost Code Extraction]
        B5 --> B6[Line Item Analysis]
        B6 --> B7[Bid Analysis Generation]
        B7 --> B8[Bid Comparison Tools]
    end

    %% Archive Flow
    subgraph ARCHIVE["📦 Archive System"]
        DB6 --> AR1{Archive Trigger}
        AR1 -->|Manual| AR2[User Archive Action]
        AR1 -->|Automated| AR3[Scheduled Archive Job]
        AR2 --> AR4[Archive Service]
        AR3 --> AR4
        AR4 --> AR5[Create ZIP Archive Stream]
        AR5 --> AR6[Collect RFQ Documents]
        AR6 --> AR7[Collect Bid Documents]
        AR7 --> AR8[Generate Archive Metadata]
        AR8 --> AR9[Stream Archive Creation]
        AR9 --> AR10[Update Archive Status]
        AR10 --> AR11[Audit Log Archive Event]
        AR11 --> AR12[Archive Complete]
    end

    %% Error Handling
    subgraph ERROR["⚠️ Error Handling"]
        U6 --> E1[Client Error Display]
        P10 --> E2{OCR Failed?}
        E2 -->|Yes| E3[Processing Failed Status]
        E3 --> E4[Error Notification]
        E4 --> E5[Retry Options]
    end

    %% External Integrations
    subgraph EXTERNAL["🔗 External Integrations"]
        M2 --> EX1[QuickBooks Integration]
        M2 --> EX2[Sage ERP Integration]
        EX1 --> EX3[Export Financial Data]
        EX2 --> EX4[Sync Project Data]
        EX3 --> EX5[API Key Authentication]
        EX4 --> EX5
        EX5 --> EX6[Data Transformation]
        EX6 --> EX7[External System Update]
    end

    %% Security & Monitoring
    subgraph SECURITY["🛡️ Security & Monitoring"]
        A8 --> SEC1[JWT Validation Middleware]
        SEC1 --> SEC2[Organization Isolation]
        SEC2 --> SEC3[Permission Checks]
        SEC3 --> SEC4[Rate Limiting]
        SEC4 --> SEC5[Audit Logging]
        SEC5 --> SEC6[Security Headers]
    end

    %% Styling
    classDef authStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef uploadStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef processStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef storageStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef errorStyle fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef archiveStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px

    class AUTH,A1,A2,A3,A4,A5,A6,A7,A8,A9,A10,A11 authStyle
    class UPLOAD,U1,U2,U3,U4,U5,U6,U7,U8,U9,U10,U11,U12,U13,U14 uploadStyle
    class PROCESS,STORAGE,AI,DB,P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,S1,S2,S3,S4,S5,S6,S7,S8 processStyle
    class ARCHIVE,AR1,AR2,AR3,AR4,AR5,AR6,AR7,AR8,AR9,AR10,AR11,AR12 archiveStyle
    class ERROR,E1,E2,E3,E4,E5 errorStyle
```

## System Flow Overview

This comprehensive flowchart visualizes the complete user journey and system flow in the Bidaible application, covering all major components and workflows from authentication to file archival.

### 🔐 **Authentication Flow**
- **Clerk-based Authentication**: Users authenticate through Clerk's OAuth system
- **JWT Token Management**: Secure token issuance and session establishment
- **Database User Sync**: Automatic user creation/update in the application database
- **Route Protection**: Middleware-based access control

### 📁 **File Upload System**
- **Multi-file Interface**: Drag-and-drop and file selection capabilities
- **Smart Storage Strategy**: Memory storage for small files (<10MB), disk storage for larger files
- **File Validation**: Content type, size, and security validation
- **Progress Tracking**: Real-time upload progress with session management

### ☁️ **Object Storage (Wasabi S3)**
- **Organized Storage Structure**: Files stored with organization and RFQ-based folder hierarchy
- **Chunked Upload**: Large files (>10MB) uploaded in chunks for reliability
- **Unique Key Generation**: Timestamp-based naming for conflict prevention
- **Progress Monitoring**: Byte-level upload progress tracking

### 🤖 **AI Processing Engine**
- **UnifiedPDFExtractor**: Multi-fallback text extraction (PDF.js → pdf-parse → Gemini Vision OCR)
- **Multi-AI Support**: Groq, OpenAI, and Gemini processing options
- **Structured Data Extraction**: AI-powered parsing of RFQ and bid documents
- **Schema Validation**: Data validation and mapping to database schema

### 🗄️ **Database Storage**
- **PostgreSQL with Drizzle ORM**: Structured data storage
- **Document References**: Links between files and extracted data
- **Processing Status Tracking**: Real-time status updates
- **Comprehensive Audit Logging**: All actions tracked for compliance

### ⚡ **Real-time Updates**
- **Server-Sent Events (SSE)**: Live progress updates to the client
- **Progress Tracker Service**: Centralized progress management
- **UI State Synchronization**: Automatic interface updates

### 💼 **Bid Management**
- **Bid Document Processing**: Similar AI processing pipeline for bid documents
- **Cost Code Extraction**: Automated line item and cost analysis
- **Bid Comparison Tools**: AI-powered bid analysis and comparison

### 📦 **Archive System**
- **Manual and Automated Archiving**: User-triggered or scheduled archival
- **ZIP Archive Creation**: Streaming archive generation with all related documents
- **Metadata Preservation**: Complete audit trail and document relationships
- **Status Tracking**: Archive completion monitoring

### 🔗 **External Integrations**
- **QuickBooks Integration**: Financial data export capabilities
- **Sage ERP Integration**: Project data synchronization
- **API Key Authentication**: Secure external system access

### 🛡️ **Security & Monitoring**
- **JWT Validation**: Continuous token verification
- **Organization Isolation**: Multi-tenant data separation
- **Rate Limiting**: API abuse prevention
- **Comprehensive Audit Logging**: Security event tracking

## Key Features Highlighted

1. **Error Handling**: Robust fallback mechanisms at every stage
2. **Progress Tracking**: Real-time feedback throughout the entire process
3. **Multi-tenant Architecture**: Organization-based data isolation
4. **Scalable Processing**: Handles both small and large file uploads efficiently
5. **AI Resilience**: Multiple AI providers with automatic fallback
6. **Complete Audit Trail**: Every action logged for compliance and debugging

## Technical Implementation Details

### API Endpoints
- `/api/clerk/user` - User authentication and database sync
- `/api/rfqs` - RFQ creation and management
- `/api/rfqs/process-documents` - Document processing endpoint
- `/api/bids` - Bid submission and management
- `/api/integrations/*` - External system integrations

### Key Services
- **clerkAuth.ts**: Authentication middleware and user management
- **objectStorageService.ts**: Wasabi S3 integration for file storage
- **aiService.ts**: AI processing orchestration
- **fileProcessingService.ts**: File upload and validation
- **archiveService.ts**: Document archival and ZIP creation
- **progressService.ts**: Real-time progress tracking

### Database Schema
- **users**: User profiles and organization associations
- **rfqs**: Request for Quote records with extracted data
- **rfq_documents**: File references and metadata
- **bids**: Bid submissions with cost analysis
- **bid_documents**: Bid-related file storage
- **audit_logs**: Comprehensive activity tracking

This diagram serves as comprehensive documentation for developers to understand the application's core functionality, data flow, and system interactions from user login through file archival.
