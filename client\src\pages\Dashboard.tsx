import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useUserRole } from "@/hooks/useUserRole";
import { DashboardStats } from "@/components/DashboardStats";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Calendar,
  MapPin,
  Clock,
  Users,
  TrendingUp,
  Eye,
  FileText,
  BarChart3,
  Target,
  DollarSign,
  CheckCircle,
  Filter,
  ArrowUpDown,
  Settings,
  Calculator,
  Brain,
  AlertTriangle,
  Download,
  Search,
} from "lucide-react";
import { tradeOptions } from "@/constants/trades";
import { format, formatDistanceToNow } from "date-fns";
import { RfqDetailView } from "@/components/RfqDetailView";
import { CountdownBadge, DualCountdownBadges } from "@/components/CountdownBadge";
import { Link } from "wouter";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import ReactMarkdown from "react-markdown";

// Embedded Bid Management Component
interface Rfq {
  id: string;
  projectName: string;
  projectLocation: string;
  dueDate: string;
  bidProposalDeadlineAt: string;
  status: string;
}

type BidAnalysisData = {
  rfq: {
    id: string;
    projectName: string;
    projectLocation: string;
    dueDate: string;
    bufferPercentage?: string | null;
    bufferNotes?: string | null;
  };
  bidSummary: {
    totalBids: number;
    acceptedBids: number;
    rejectedBids: number;
    baseTotal: number;
    bufferAmount: number;
    totalWithBuffer: number;
  };
  bids: Array<{
    bid: any;
    contractor: { companyName: string; primaryContactName?: string; primaryContactEmail?: string };
  }>;
  aiAnalysis?: any;
};

function BidManagementEmbedded({
  rfqs,
  isGeneralContractor,
}: {
  rfqs: Rfq[];
  isGeneralContractor: boolean;
}) {
  const [selectedRfqId, setSelectedRfqId] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState("submittedAt");
  const [statusFilter, setStatusFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");

  const [bufferDialogOpen, setBufferDialogOpen] = useState(false);
  const [bufferPercentage, setBufferPercentage] = useState("10.00");
  const [bufferNotes, setBufferNotes] = useState("");
  const [activeTab, setActiveTab] = useState("overview");

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Handle export functionality
  const handleExport = async (format: "csv" | "pdf") => {
    console.log("🚀 Dashboard Export initiated:", { format, hasAnalysisData: !!analysisData });

    if (!analysisData || !analysisData.rfq) {
      console.log("❌ No analysis data to export:", {
        analysisData: !!analysisData,
        rfq: analysisData?.rfq,
      });
      toast({ title: "No data to export", variant: "destructive" });
      return;
    }

    try {
      console.log("📥 Importing dashboard report utilities...");
      const { exportBidDataCSV, generateBidReportPDF, exportAcceptedBidDetailsCSV } = await import(
        "@/lib/reportUtils"
      );
      console.log("✅ Dashboard report utilities imported successfully");

      if (format === "csv") {
        // Check if there are accepted bids - if so, export detailed cost code breakdown
        const acceptedBids =
          analysisData.bids?.filter(
            ({ bid }: any) => bid.status === "accepted" || bid.status === "accept"
          ) || [];

        if (acceptedBids.length > 0) {
          console.log("📄 Starting Accepted Bid Details CSV export:", {
            rfqTitle: analysisData.rfq?.projectName,
            acceptedBidsCount: acceptedBids.length,
            rfqId: selectedRfqId || rfqId,
          });
          await exportAcceptedBidDetailsCSV(selectedRfqId || rfqId);
          console.log("✅ Accepted bid details CSV export completed");
          toast({ title: "Accepted bid details CSV downloaded successfully" });
        } else {
          console.log("📄 Starting Dashboard CSV export with analysisData:", {
            rfqTitle: analysisData.rfq?.projectName,
            bidsCount: analysisData.bids?.length || 0,
            bidSummary: analysisData.bidSummary,
          });
          await exportBidDataCSV(analysisData);
          console.log("✅ Dashboard CSV export completed");
          toast({ title: "CSV report downloaded successfully" });
        }
      } else {
        console.log("📄 Starting Dashboard PDF export with analysisData:", {
          rfqTitle: analysisData.rfq?.projectName,
          bidsCount: analysisData.bids?.length || 0,
        });
        await generateBidReportPDF(analysisData);
        console.log("✅ Dashboard PDF export completed");
        toast({ title: "PDF report generated - check print dialog" });
      }
    } catch (error) {
      const errorInfo =
        error instanceof Error
          ? {
              message: error.message,
              stack: error.stack,
              name: error.name,
            }
          : { message: String(error) };

      console.error("❌ Dashboard Export failed with detailed error:", {
        ...errorInfo,
        format,
        hasAnalysisData: !!analysisData,
        analysisDataStructure: analysisData
          ? {
              hasRfq: !!analysisData.rfq,
              hasBids: !!analysisData.bids,
              hasBidSummary: !!analysisData.bidSummary,
              bidsLength: analysisData.bids?.length || 0,
            }
          : null,
      });
      toast({
        title: "Export failed",
        description: error instanceof Error ? error.message : "Please try again",
        variant: "destructive",
      });
    }
  };

  // Find the first active RFQ with bids
  const activeRfqWithBids = rfqs.find((rfq: Rfq) => rfq.status === "Active");
  const rfqId = selectedRfqId || activeRfqWithBids?.id || "747e0b12-fa33-474e-9f0d-d14fef261200";

  // Fetch bid analysis data
  const { data: analysisData, isLoading } = useQuery<BidAnalysisData>({
    queryKey: ["/api/rfqs", rfqId, "bids", "analysis"],
    enabled: !!rfqId,
  });

  // Set buffer data when loaded
  useEffect(() => {
    if (analysisData && analysisData.rfq) {
      setBufferPercentage(analysisData.rfq.bufferPercentage || "10.00");
      setBufferNotes(analysisData.rfq.bufferNotes || "");
    }
  }, [analysisData]);

  // Bid action mutation
  const bidActionMutation = useMutation({
    mutationFn: async ({ bidId, action }: any) => {
      return apiRequest("PATCH", `/api/bids/${bidId}`, {
        status: action,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/rfqs", rfqId, "bids", "analysis"] });
      toast({ title: "Bid action completed successfully" });
    },
    onError: (error: any) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    },
  });

  // Buffer update mutation
  const bufferUpdateMutation = useMutation({
    mutationFn: async (data: { bufferPercentage: string; bufferNotes: string }) => {
      return apiRequest("PUT", `/api/rfqs/${rfqId}/buffer`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/rfqs", rfqId, "bids", "analysis"] });
      setBufferDialogOpen(false);
      toast({ title: "Buffer settings updated successfully" });
    },
    onError: (error: any) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    },
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Bid Management Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="grid grid-cols-4 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="h-20 bg-muted rounded-lg"></div>
              ))}
            </div>
            <div className="h-40 bg-muted rounded-lg"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!analysisData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Bid Management Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>Select an RFQ to view bid management</p>
            <div className="mt-4">
              <Select value={selectedRfqId || ""} onValueChange={setSelectedRfqId}>
                <SelectTrigger className="w-full max-w-md mx-auto">
                  <SelectValue placeholder="Select an RFQ" />
                </SelectTrigger>
                <SelectContent>
                  {rfqs
                    .filter((rfq: Rfq) => rfq.status === "Active")
                    .map((rfq: Rfq) => (
                      <SelectItem key={rfq.id} value={rfq.id}>
                        {rfq.projectName}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { rfq, bidSummary, bids } = analysisData || { rfq: null, bidSummary: null, bids: [] };

  // Status normalization function
  const normalizeStatus = (status: string) => {
    const normalized = status.toLowerCase();
    switch (normalized) {
      case "accept":
      case "accepted":
        return "accepted";
      case "reject":
      case "rejected":
        return "rejected";
      case "submitted":
        return "submitted";
      case "pending":
        return "pending";
      case "request info":
      case "requestinfo":
        return "request info";
      default:
        return normalized;
    }
  };

  // Debug logging
  console.log(
    `🚀 Dashboard BidManagement - Total bids: ${bids?.length || 0}, Status filter: "${statusFilter}"`
  );
  if (bids?.length > 0) {
    console.log(
      "📋 Bid statuses:",
      bids.map(({ bid }: any) => `${bid.id.slice(0, 8)}: "${bid.status}"`)
    );
  }

  // Filter and sort bids
  const filteredBids = bids.filter(({ bid, contractor }: any) => {
    // Status filter
    if (statusFilter !== "all") {
      const normalizedBidStatus = normalizeStatus(bid.status);
      const normalizedFilter = statusFilter.toLowerCase();

      // Debug logging for each bid
      console.log(
        `🔍 Filter Debug - Bid ID: ${bid.id.slice(0, 8)}, Status: "${bid.status}" -> normalized: "${normalizedBidStatus}", filter: "${normalizedFilter}", match: ${normalizedBidStatus === normalizedFilter}`
      );

      // Handle special cases
      if (statusFilter === "pending") {
        if (["accepted", "rejected", "submitted"].includes(normalizedBidStatus)) return false;
      } else {
        if (normalizedBidStatus !== normalizedFilter) return false;
      }
    }

    // Search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      const contractorName = contractor.companyName?.toLowerCase() || "";
      const contactName = contractor.primaryContactName?.toLowerCase() || "";
      const bidAmount = (bid.bidAmount || bid.extractedAmount || "").toString().toLowerCase();
      const timeline = (bid.timeline || bid.extractedTimeline || "").toLowerCase();

      return (
        contractorName.includes(searchLower) ||
        contactName.includes(searchLower) ||
        bidAmount.includes(searchLower) ||
        timeline.includes(searchLower)
      );
    }

    return true;
  });

  // Helper functions for sorting
  function getPrimaryTradeType(tradeTypes: any): string {
    if (!tradeTypes || tradeTypes.length === 0) return "No category";
    const primaryTrade = Array.isArray(tradeTypes) ? tradeTypes[0] : tradeTypes;
    const tradeOption = tradeOptions.find((option) => option.value === primaryTrade);
    return tradeOption?.label || "Unknown";
  }

  function formatTradeTypes(tradeTypes: any): string {
    if (!tradeTypes || tradeTypes.length === 0) return "No category";

    const trades = Array.isArray(tradeTypes) ? tradeTypes : [tradeTypes];
    const primaryTrade = tradeOptions.find((option) => option.value === trades[0]);
    const primaryLabel = primaryTrade?.label || "Unknown";

    if (trades.length === 1) {
      return primaryLabel;
    } else {
      return `${primaryLabel} +${trades.length - 1}`;
    }
  }

  const sortedBids = [...filteredBids].sort((a: any, b: any) => {
    let aValue, bValue;

    // Handle special cases for different sort types
    if (sortBy === "bidAmount") {
      aValue = a.bid.extractedAmount || a.bid.bidAmount || 0;
      bValue = b.bid.extractedAmount || b.bid.bidAmount || 0;
    } else if (sortBy === "contractorName") {
      aValue = a.contractor.companyName?.toLowerCase() || "";
      bValue = b.contractor.companyName?.toLowerCase() || "";
    } else if (sortBy === "contractorCategory") {
      aValue = getPrimaryTradeType(a.contractor.tradeTypes);
      bValue = getPrimaryTradeType(b.contractor.tradeTypes);
    } else {
      aValue = a.bid[sortBy as keyof typeof a.bid];
      bValue = b.bid[sortBy as keyof typeof b.bid];
    }

    console.log(`🔄 Sort Debug - sortBy: "${sortBy}", aValue: ${aValue}, bValue: ${bValue}`);

    if (sortBy === "submittedAt") {
      return new Date(bValue as string).getTime() - new Date(aValue as string).getTime();
    }

    if (sortBy === "status") {
      // Define status priority: pending > submitted > accepted > rejected
      const statusPriority = { pending: 4, submitted: 3, accepted: 2, rejected: 1 };
      const aStatus = normalizeStatus(aValue as string);
      const bStatus = normalizeStatus(bValue as string);
      return (
        (statusPriority[bStatus as keyof typeof statusPriority] || 0) -
        (statusPriority[aStatus as keyof typeof statusPriority] || 0)
      );
    }

    if (sortBy === "contractorName" || sortBy === "contractorCategory") {
      return String(aValue).localeCompare(String(bValue));
    }

    if (typeof aValue === "number" && typeof bValue === "number") {
      return bValue - aValue;
    }

    return String(bValue).localeCompare(String(aValue));
  });

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Bid Management Dashboard</CardTitle>
            <p className="text-sm text-muted-foreground truncate max-w-md">{rfq.projectName}</p>
            <p className="text-xs text-muted-foreground">
              {rfq.projectLocation} • Due {format(new Date(rfq.dueDate), "MMM dd, yyyy")}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport("csv")}
              className="bg-green-50 hover:bg-green-100 border-green-200"
            >
              <Download className="h-3 w-3 mr-1" />
              Export CSV
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport("pdf")}
              className="bg-blue-50 hover:bg-blue-100 border-blue-200"
            >
              <Download className="h-3 w-3 mr-1" />
              Export PDF
            </Button>
            <Select value={selectedRfqId || rfqId} onValueChange={setSelectedRfqId}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {rfqs
                  .filter((rfq) => rfq.status === "Active")
                  .map((rfq: any) => (
                    <SelectItem key={rfq.id} value={rfq.id}>
                      {rfq.projectName}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
            <Dialog open={bufferDialogOpen} onOpenChange={setBufferDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-1" />
                  Buffer Settings
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Manage Cost Buffer</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="bufferPercentage">Buffer Percentage (%)</Label>
                    <Input
                      id="bufferPercentage"
                      type="number"
                      step="0.01"
                      value={bufferPercentage}
                      onChange={(e) => setBufferPercentage(e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="bufferNotes">Buffer Notes</Label>
                    <Textarea
                      id="bufferNotes"
                      value={bufferNotes}
                      onChange={(e) => setBufferNotes(e.target.value)}
                      placeholder="Explain the reasoning for this buffer percentage..."
                    />
                  </div>
                  <Button
                    onClick={() => bufferUpdateMutation.mutate({ bufferPercentage, bufferNotes })}
                    disabled={bufferUpdateMutation.isPending}
                  >
                    {bufferUpdateMutation.isPending ? "Updating..." : "Update Buffer"}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          {!isGeneralContractor && (
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Overview
              </TabsTrigger>
              {!isGeneralContractor && (
                <TabsTrigger value="bids" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Bid Analysis
                </TabsTrigger>
              )}
              {!isGeneralContractor && (
                <TabsTrigger value="ai-analysis" className="flex items-center gap-2">
                  <Brain className="h-4 w-4" />
                  AI Analysis
                </TabsTrigger>
              )}
            </TabsList>
          )}

          <TabsContent value="overview" className="space-y-6 mt-6">
            {/* Summary Stats */}
            <div className="grid grid-cols-4 gap-4">
              <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                <div className="p-2 bg-blue-500/10 rounded-lg">
                  <FileText className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Total Bids</p>
                  <p className="text-2xl font-bold">{bidSummary.totalBids}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                <div className="p-2 bg-green-500/10 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Accepted Bids</p>
                  <p className="text-2xl font-bold">{bidSummary.acceptedBids}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                <div className="p-2 bg-green-500/10 rounded-lg">
                  <DollarSign className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Base Total</p>
                  <p className="text-2xl font-bold">${bidSummary.baseTotal.toLocaleString()}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                <div className="p-2 bg-orange-500/10 rounded-lg">
                  <Calculator className="h-4 w-4 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Total + Buffer</p>
                  <p className="text-2xl font-bold text-orange-600">
                    ${bidSummary.totalWithBuffer.toLocaleString()}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    +${bidSummary.bufferAmount.toLocaleString()} (
                    {((bidSummary.bufferAmount / bidSummary.baseTotal) * 100).toFixed(1)}%)
                  </p>
                </div>
              </div>
            </div>

            {/* Filters and Controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 flex-wrap gap-2">
                <div className="flex items-center space-x-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Label htmlFor="search" className="text-sm">
                    Search:
                  </Label>
                  <Input
                    id="search"
                    placeholder="Search contractors, amounts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-64"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Filter className="h-4 w-4 text-muted-foreground" />
                  <Label htmlFor="status-filter" className="text-sm">
                    Filter by Status:
                  </Label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Bids</SelectItem>
                      <SelectItem value="submitted">Submitted</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="accepted">Accepted</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
                  <Label htmlFor="sort-by" className="text-sm">
                    Sort by:
                  </Label>
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="submittedAt">Submission Date</SelectItem>
                      <SelectItem value="contractorName">Contractor Name</SelectItem>
                      <SelectItem value="contractorCategory">Contractor Category</SelectItem>
                      <SelectItem value="bidAmount">Bid Amount</SelectItem>
                      <SelectItem value="status">Status</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Bids Table */}
            <div className="border rounded-lg">
              <div className="p-4 border-b bg-muted/30">
                <h3 className="font-semibold">Bid Submissions ({sortedBids.length})</h3>
              </div>

              {sortedBids.length === 0 ? (
                <div className="p-8 text-center text-muted-foreground">
                  <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No bids match your current filters</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Contractor</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Bid Amount</TableHead>
                      <TableHead>Timeline</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedBids.map(({ bid, contractor }: any) => (
                      <TableRow key={bid.id} className="hover:bg-muted/30">
                        <TableCell>
                          <div>
                            <p className="font-medium">{contractor.companyName}</p>
                            <p className="text-sm text-muted-foreground">
                              {contractor.primaryContactName}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">{formatTradeTypes(contractor.tradeTypes)}</span>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium">
                            $
                            {Number(bid.extractedAmount || bid.bidAmount || 0).toLocaleString(
                              "en-US",
                              { maximumFractionDigits: 0 }
                            )}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">{bid.timeline || "—"}</span>
                        </TableCell>
                        <TableCell>
                          <span
                            className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold ${
                              bid.status === "Accepted" ||
                              bid.status === "accepted" ||
                              bid.status === "accept"
                                ? "border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-200"
                                : bid.status === "Rejected" ||
                                    bid.status === "rejected" ||
                                    bid.status === "reject"
                                  ? "border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-900/20 dark:text-red-200"
                                  : "border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-200"
                            }`}
                          >
                            {bid.status === "accept"
                              ? "Accepted"
                              : bid.status === "reject"
                                ? "Rejected"
                                : bid.status.charAt(0).toUpperCase() + bid.status.slice(1)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {formatDistanceToNow(new Date(bid.submittedAt), { addSuffix: true })}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Link href={`/rfqs/${rfqId}/bids?bidId=${bid.id}`}>
                            <Button variant="outline" size="sm">
                              <Eye className="h-3 w-3 mr-1" />
                              Review
                            </Button>
                          </Link>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>
          </TabsContent>

          {!isGeneralContractor && (
            <TabsContent value="bids" className="space-y-6 mt-6">
              <div className="text-center py-8 text-muted-foreground">
                <BarChart3 className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <h3 className="font-medium">Bid Analysis</h3>
                <p className="text-sm">Detailed bid comparison and market analysis coming soon.</p>
              </div>
            </TabsContent>
          )}

          {!isGeneralContractor && (
            <TabsContent value="ai-analysis" className="space-y-6 mt-6">
              {isLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Generating AI analysis...</p>
                </div>
              ) : analysisData && analysisData.aiAnalysis ? (
                <div className="space-y-6">
                  {/* AI Executive Summary Panel */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Brain className="h-5 w-5 text-primary" />
                        <span>AI Executive Summary</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-medium text-sm uppercase tracking-wide text-muted-foreground mb-2">
                          Overview
                        </h4>
                        <p className="text-sm leading-relaxed">
                          {(analysisData &&
                            analysisData.aiAnalysis?.aiExecutiveSummary?.overview) ||
                            "No overview available"}
                        </p>
                      </div>

                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-medium text-sm uppercase tracking-wide text-muted-foreground mb-2">
                            Key Insights
                          </h4>
                          <ul className="space-y-1">
                            {(analysisData &&
                              analysisData.aiAnalysis?.aiExecutiveSummary?.keyInsights?.map(
                                (insight: string, index: number) => (
                                  <li key={index} className="flex items-start space-x-2">
                                    <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                                    <span className="text-sm">{insight}</span>
                                  </li>
                                )
                              )) ||
                              []}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-sm uppercase tracking-wide text-muted-foreground mb-2">
                            Recommendations
                          </h4>
                          <ul className="space-y-1">
                            {(analysisData &&
                              analysisData.aiAnalysis?.aiExecutiveSummary?.recommendations?.map(
                                (rec: string, index: number) => (
                                  <li key={index} className="flex items-start space-x-2">
                                    <TrendingUp className="h-3 w-3 text-blue-500 mt-0.5 flex-shrink-0" />
                                    <span className="text-sm">{rec}</span>
                                  </li>
                                )
                              )) ||
                              []}
                          </ul>
                        </div>
                      </div>

                      {analysisData &&
                        analysisData.aiAnalysis?.aiExecutiveSummary?.riskFactors?.length > 0 && (
                          <div>
                            <h4 className="font-medium text-sm uppercase tracking-wide text-muted-foreground mb-2">
                              Risk Factors
                            </h4>
                            <ul className="space-y-1">
                              {analysisData.aiAnalysis.aiExecutiveSummary.riskFactors.map(
                                (risk: string, index: number) => (
                                  <li key={index} className="flex items-start space-x-2">
                                    <AlertTriangle className="h-3 w-3 text-orange-500 mt-0.5 flex-shrink-0" />
                                    <span className="text-sm">{risk}</span>
                                  </li>
                                )
                              )}
                            </ul>
                          </div>
                        )}
                    </CardContent>
                  </Card>

                  {/* AI Bid Ranking */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <BarChart3 className="h-5 w-5 text-primary" />
                        <span>AI Bid Ranking</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {analysisData &&
                          analysisData.aiAnalysis?.bidRanking?.rankedBids?.map(
                            (rankedBid: any, index: number) => {
                              const bidData = bids.find((b: any) => b.bid.id === rankedBid.bidId);
                              if (!bidData) return null;

                              return (
                                <div key={rankedBid.bidId} className="border rounded-lg p-4">
                                  <div className="flex items-center justify-between mb-3">
                                    <div className="flex items-center space-x-3">
                                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-medium">
                                        #{index + 1}
                                      </div>
                                      <div>
                                        <h4 className="font-medium">
                                          {bidData.contractor.companyName}
                                        </h4>
                                        <p className="text-sm text-muted-foreground">
                                          $
                                          {(bidData.bid.extractedAmount || 0).toLocaleString(
                                            "en-US",
                                            { maximumFractionDigits: 0 }
                                          )}
                                        </p>
                                      </div>
                                    </div>

                                    <div className="flex items-center space-x-3">
                                      <Badge
                                        variant={
                                          rankedBid.riskLevel === "low"
                                            ? "default"
                                            : rankedBid.riskLevel === "medium"
                                              ? "secondary"
                                              : "destructive"
                                        }
                                        className={
                                          rankedBid.riskLevel === "low"
                                            ? "bg-green-100 text-green-800 hover:bg-green-200"
                                            : rankedBid.riskLevel === "medium"
                                              ? "bg-yellow-100 text-yellow-800 hover:bg-yellow-200"
                                              : "bg-red-100 text-red-800 hover:bg-red-200"
                                        }
                                      >
                                        {rankedBid.riskLevel} risk
                                      </Badge>

                                      <div className="flex items-center space-x-1">
                                        <TrendingUp className="h-4 w-4 text-green-600" />
                                        <span className="font-medium text-lg">
                                          {rankedBid.aiScore}/100
                                        </span>
                                      </div>
                                    </div>
                                  </div>

                                  <div className="space-y-2">
                                    <p className="text-sm">{rankedBid.reasoning}</p>
                                    <p className="text-sm font-medium text-primary">
                                      {rankedBid.competitivePosition}
                                    </p>
                                  </div>
                                </div>
                              );
                            }
                          )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Market Analysis */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <DollarSign className="h-5 w-5 text-primary" />
                        <span>Market Analysis</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-medium mb-3">Price Spread Analysis</h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm text-muted-foreground">Lowest Bid:</span>
                              <span className="font-medium">
                                $
                                {(analysisData &&
                                  analysisData.aiAnalysis?.marketAnalysis?.priceSpread?.min?.toLocaleString()) ||
                                  "0"}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-muted-foreground">Highest Bid:</span>
                              <span className="font-medium">
                                $
                                {(analysisData &&
                                  analysisData.aiAnalysis?.marketAnalysis?.priceSpread?.max?.toLocaleString()) ||
                                  "0"}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-muted-foreground">Average:</span>
                              <span className="font-medium">
                                $
                                {analysisData &&
                                analysisData.aiAnalysis?.marketAnalysis?.priceSpread?.avg
                                  ? Math.round(
                                      analysisData.aiAnalysis.marketAnalysis.priceSpread.avg
                                    ).toLocaleString()
                                  : "0"}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-muted-foreground">Median:</span>
                              <span className="font-medium">
                                $
                                {analysisData &&
                                analysisData.aiAnalysis?.marketAnalysis?.priceSpread?.median
                                  ? Math.round(
                                      analysisData.aiAnalysis.marketAnalysis.priceSpread.median
                                    ).toLocaleString()
                                  : "0"}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium mb-3">Strategic Assessment</h4>
                          <div className="space-y-3">
                            <div>
                              <p className="text-sm text-muted-foreground mb-1">
                                Competitive Positioning:
                              </p>
                              <p className="text-sm">
                                {(analysisData &&
                                  analysisData.aiAnalysis?.marketAnalysis
                                    ?.competitivePositioning) ||
                                  "Not available"}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground mb-1">Risk Assessment:</p>
                              <p className="text-sm">
                                {(analysisData &&
                                  analysisData.aiAnalysis?.marketAnalysis?.riskAssessment) ||
                                  "Not available"}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Brain className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <h3 className="font-medium">No AI Analysis Available</h3>
                  <p className="text-sm">
                    Submit bids to this RFQ to generate AI-powered insights and analysis.
                  </p>
                </div>
              )}
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
}

export default function Dashboard() {
  const { user } = useAuth();
  const { isGeneralContractor } = useUserRole();
  const [selectedRfqId, setSelectedRfqId] = useState<string | null>(null);

  const { data: rfqs = [], isLoading } = useQuery<Rfq[]>({
    queryKey: ["/api/rfqs"],
  });

  const recentRfqs = rfqs.slice(0, 5);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
      case "Draft":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
      case "Review":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";
      case "Closed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {user?.firstName}! Monitor your RFQ performance and project activity.
          </p>
        </div>
      </div>

      {/* Stats Cards - Hidden for General Contractors */}
      {!isGeneralContractor && <DashboardStats />}

      {/* Full Width Bid Management Dashboard */}
      <BidManagementEmbedded rfqs={recentRfqs} isGeneralContractor={isGeneralContractor} />

      {/* Recent RFQs Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Recent RFQs</CardTitle>
            <Button variant="outline" size="sm">
              View All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div
                  key={i}
                  className="flex items-center space-x-4 p-4 rounded-lg border animate-pulse"
                >
                  <div className="h-10 w-10 bg-muted rounded-lg"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-muted rounded w-1/3"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {recentRfqs.map((rfq: any) => (
                <div
                  key={rfq.id}
                  className="flex items-center space-x-4 p-4 rounded-lg border hover:bg-muted/50"
                >
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <span className="text-primary font-medium text-sm">
                      {rfq.projectName?.charAt(0) || "R"}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium truncate">{rfq.projectName}</h4>
                      <Badge className={getStatusColor(rfq.status)}>{rfq.status}</Badge>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <MapPin className="h-3 w-3" />
                        <span>{rfq.projectLocation}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>Due {format(new Date(rfq.dueDate), "MMM dd")}</span>
                      </div>
                    </div>
                    <div className="mt-2">
                      {rfq.bidProposalDeadlineAt ? (
                        <DualCountdownBadges
                          bidProposalDeadline={rfq.bidProposalDeadlineAt}
                          rfqDeadline={rfq.dueDate}
                          className="flex-wrap gap-1"
                        />
                      ) : (
                        <CountdownBadge
                          targetDate={rfq.dueDate}
                          label="RFQ closes in"
                          format="short"
                        />
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">0 bids</span>

                    {/* View Details Button */}
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedRfqId(rfq.id)}
                          className="ml-2"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          View Details
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>{rfq.projectName}</DialogTitle>
                          <DialogDescription>
                            View detailed information about this RFQ
                          </DialogDescription>
                        </DialogHeader>
                        {selectedRfqId && <RfqDetailView rfqId={selectedRfqId} />}
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              ))}
              {recentRfqs.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    No RFQs found. Create your first RFQ to get started.
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
