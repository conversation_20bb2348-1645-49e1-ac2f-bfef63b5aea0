import type { Express } from "express";
import { storage } from "../storage";
import { generateApiKey } from "../services/apiKeyService";
import { readOnlyApiAccess } from "../middleware/apiAuth";

export function addTestFixtures(app: Express) {
  if (process.env.NODE_ENV !== "development") return;

  app.post("/api/test/org", async (req: any, res) => {
    try {
      const { name, slug, id } = req.body || {};
      if (!name) return res.status(400).json({ message: "name required" });
      const org = await storage.createOrganization({ id, name, slug: slug || name.toLowerCase().replace(/[^a-z0-9-]/g, '-') } as any);
      res.json(org);
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed' });
    }
  });

  app.post("/api/test/user", async (req: any, res) => {
    try {
      const { id, email, organizationId, userClassification } = req.body || {};
      if (!id) return res.status(400).json({ message: "id required" });
      const user = await storage.upsertUser({ id, email: email || `${id}@example.com`, organizationId, userClassification } as any);
      res.json(user);
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed' });
    }
  });

  app.post("/api/test/contractor", async (req: any, res) => {
    try {
      const b = req.body || {};
      const payload: any = {
        id: b.id,
        userId: b.userId,
        organizationId: b.organizationId,
        companyName: b.companyName || 'Contractor Co',
        primaryContactName: b.primaryContactName || null,
        primaryContactEmail: b.primaryContactEmail || null,
        primaryContactPhone: b.primaryContactPhone || null,
        tradeTypes: b.tradeTypes || ['general'],
        isApproved: b.isApproved ?? true,
      };
      const contractor = await storage.createContractor(payload);
      res.json(contractor);
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed', stack: e?.stack || null });
    }
  });

  app.post("/api/test/rfq-simple", async (req: any, res) => {
    try {
      const { organizationId, createdBy } = req.body || {};
      const dueDate = new Date(Date.now() + 7*24*60*60*1000);
      const bidProposalDeadlineAt = new Date(dueDate.getTime() - 24*60*60*1000); // 1 day before
      const rfq = await storage.createRfq({
        organizationId,
        createdBy,
        projectName: 'Test Project',
        projectLocation: 'Test City',
        tradeCategory: 'general',
        description: null,
        status: 'Active',
        dueDate,
        bidProposalDeadlineAt,
      } as any);
      res.json(rfq);
    } catch (e: any) {
      console.error('DEV FIXTURE error at /api/test/rfq-simple:', e);
      res.status(500).json({ message: e?.message || 'failed', stack: e?.stack || null });
    }
  });

  app.post("/api/test/rfq", async (req: any, res) => {
    try {
      const b = req.body || {};
      const payload: any = {
        id: b.id,
        createdBy: b.createdBy,
        organizationId: b.organizationId,
        projectName: b.projectName || 'Test Project',
        projectLocation: b.projectLocation || 'Test City',
        tradeCategory: b.tradeCategory || 'general',
        description: b.description || null,
        status: b.status || 'Active',
      };
      const dd = b.dueDate;
      payload.dueDate = dd ? (typeof dd === 'string' ? new Date(dd) : new Date(dd)) : new Date();
      payload.bidProposalDeadlineAt = b.bidProposalDeadlineAt 
        ? (typeof b.bidProposalDeadlineAt === 'string' ? new Date(b.bidProposalDeadlineAt) : new Date(b.bidProposalDeadlineAt))
        : new Date(payload.dueDate.getTime() - 24*60*60*1000); // 1 day before due date
      const rfq = await storage.createRfq(payload);
      res.json(rfq);
    } catch (e: any) {
    console.error('DEV FIXTURE error at /api/test/rfq:', e);
      res.status(500).json({ message: e?.message || 'failed', stack: e?.stack || null });
      }
     });
    
  app.post("/api/test/rfq-document", async (req: any, res) => {
    try {
      const b = req.body || {};
      const payload: any = {
        id: b.id,
        rfqId: b.rfqId,
        fileName: b.fileName || 'dummy.pdf',
        objectKey: b.objectKey || `org/${b.organizationId || 'org'}/rfq/${b.rfqId || 'rfq'}/dummy.pdf`,
        fileSize: b.fileSize || 1024,
        mimeType: b.mimeType || 'application/pdf',
      };
      const doc = await storage.createRfqDocument(payload);
      res.json(doc);
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed', stack: e?.stack || null });
    }
  });

  app.post("/api/test/distribute", async (req: any, res) => {
    try {
      const { rfqId, contractorIds, method } = req.body || {};
      const rows = await storage.distributeRfq(rfqId, contractorIds || [], method || 'favorites');
      res.json(rows);
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed' });
    }
  });

  app.post("/api/test/api-key", async (req: any, res) => {
    try {
      const { userId, permissions = 'full-access', rateLimit = 1000 } = req.body || {};
      if (!userId) return res.status(400).json({ message: "userId required" });
      const result = await generateApiKey(userId, `Test Key ${Date.now()}`, permissions as any, rateLimit, { expirationDays: 365, environment: 'development' } as any);
      res.json(result);
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed' });
    }
  });

  app.get("/api/test/whoami", ...readOnlyApiAccess as any, (req: any, res) => {
    try {
      res.json({ user: req.user, ok: true });
    } catch (e: any) {
      res.status(500).json({ message: e?.message || 'failed' });
    }
  });

  app.get("/api/test/ping", (_req: any, res) => {
    res.json({ ok: true, env: process.env.NODE_ENV || null });
  });


}

