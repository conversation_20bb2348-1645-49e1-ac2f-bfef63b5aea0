# AI Processing Safeguards & Testing Protocol

## Critical AI Processing Components - DO NOT MODIFY WITHOUT TESTING

### Core Files That Affect AI Summary Generation
1. **server/services/aiOptimizedService.ts** - Primary AI processing service
   - `processRfqDocumentOptimized()` function
   - `processWithModel()` function  
   - Token limits and text truncation logic

2. **server/services/core/pdfExtractor.ts** - PDF text extraction
   - Environment-specific extraction methods
   - Fallback chain: PDF.js → pdf-parse → Gemini Vision

3. **server/routes.ts** - RFQ creation endpoint
   - Data merging logic in `Object.assign(allExtractedData, result.structuredData)`
   - AI summary storage: `aiSummary: cleanString(allExtractedData.aiSummary) || null`

## Testing Protocol for AI Changes

### Before Making Changes
```bash
# 1. Test current AI processing works
curl -X POST http://localhost:5000/api/test/rfqs -F "documents=@test.pdf" -F "projectName=Test"

# 2. Verify AI summary in database
# Check ai_summary column in rfqs table via database client
```

### After Making Changes
```bash
# 1. Run type check
npm run check

# 2. Test AI processing
npm run dev
# Upload test document via UI
# Verify AI summary appears in RFQ details modal

# 3. Test production environment simulation
NODE_ENV=production npm run dev:api
# Test with production PDF processing (pdf-parse only)

# 4. Run AI tests
cd tests && npm run test:ai
```

### Database Verification Script
```sql
-- Check most recent RFQ has AI summary
SELECT id, project_name, ai_summary IS NOT NULL as has_ai_summary, 
       LENGTH(ai_summary) as summary_length, created_at
FROM rfqs 
ORDER BY created_at DESC 
LIMIT 5;
```

## High-Risk Changes That Require Extra Testing

### Token Limit Modifications
- **Risk**: May cause AI models to fail or return incomplete responses
- **Test**: Upload large documents (>100k characters) and verify summaries generate
- **Models Affected**: Groq (strictest limits), OpenAI, Gemini

### Text Truncation Logic
- **Risk**: May remove critical content needed for AI analysis
- **Test**: Upload complex multi-page documents and verify key details extracted
- **Function**: `applySmartTruncation()` in aiOptimizedService.ts

### PDF Extraction Changes
- **Risk**: May break text extraction, causing AI to fail
- **Test**: Upload various PDF types (text, scanned, complex layouts)
- **Environment**: Test both development (PDF.js) and production (pdf-parse)

### Data Merging & Storage
- **Risk**: May prevent AI summary from reaching database
- **Test**: Verify `allExtractedData.aiSummary` → database `ai_summary` column
- **Function**: RFQ creation logic in routes.ts

## Rollback Procedures

### If AI Processing Breaks
1. **Identify Last Working Commit**
   ```bash
   git log --oneline server/services/aiOptimizedService.ts
   ```

2. **Revert Specific Changes**
   ```bash
   git checkout HEAD~1 -- server/services/aiOptimizedService.ts
   ```

3. **Test Immediately**
   - Upload test document
   - Verify AI summary appears
   - Check database contains summary

### Emergency Hotfix Process
1. **Disable AI processing temporarily** (if needed):
   ```javascript
   // In aiOptimizedService.ts - emergency fallback
   return {
     text: extractedText,
     structuredData: { fileName, aiSummary: "AI processing temporarily disabled" },
     success: true,
     model: 'fallback'
   };
   ```

2. **Deploy minimal fix** to restore basic functionality
3. **Fix properly** in development environment

## Monitoring & Alerts

### Key Metrics to Monitor
- **AI Summary Generation Rate**: Should be >90% for valid PDFs
- **Processing Time**: Should be <30 seconds for typical documents  
- **Model Success Rate**: Track which AI models are failing
- **Database Storage**: Verify ai_summary column population

### Warning Signs
- Multiple "No AI summary found" warnings in logs
- Increased processing times (>60 seconds)
- Model fallback chains activating frequently
- Empty ai_summary fields in database

## Code Review Checklist for AI Changes

### Required Checks
- [ ] Does change affect token limits or text truncation?
- [ ] Does change modify PDF extraction logic?
- [ ] Does change alter data merging in routes.ts?
- [ ] Are AI model configurations changed?
- [ ] Are environment-specific behaviors modified?

### Testing Requirements
- [ ] Test with small document (<10k chars)
- [ ] Test with large document (>100k chars)  
- [ ] Test with scanned PDF (requires OCR)
- [ ] Verify AI summary in UI after upload
- [ ] Check database ai_summary column populated
- [ ] Test both development and production environments

---

**Remember**: AI processing is the core value proposition. Any changes that break AI summary generation significantly impact user experience and product value.
