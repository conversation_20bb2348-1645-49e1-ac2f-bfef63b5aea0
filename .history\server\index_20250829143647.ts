import { config } from "dotenv";
import path from "path";
import { fileURLToPath } from "url";
import express, { type Request, Response, NextFunction } from "express";
import * as Sentry from "@sentry/node";
import { registerRoutes } from "./routes";
import { addHealthRoutes } from "./routes/health";
import { setupVite, serveStatic } from "./vite";
import {
  auditLogger,
  rateLimiter,
  securityHeaders,
  sanitizeInput,
  corsOptions,
  helmetOptions,
} from "./middleware/security";
import { scheduleAutomaticBackups } from "./services/backupService";
import { scheduledNotificationProcessor } from "./services/scheduledNotificationProcessor";
import helmet from "helmet";
import cors from "cors";
import compression from "compression";
import logger from "./utils/logger";

// ESM-compatible __dirname/__filename for this module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load .env from root directory only in local development
// Railway provides environment variables directly, no .env file needed
// Check for Railway environment indicators to determine if we're in Railway deployment
const isRailwayDeployment = !!(
  process.env.RAILWAY_ENVIRONMENT ||
  process.env.RAILWAY_PROJECT_ID ||
  process.env.NIXPACKS_METADATA
);

if (!isRailwayDeployment && process.env.NODE_ENV !== "production") {
  // Only load .env if we're in local development (not Railway)
  config({ path: path.resolve(process.cwd(), ".env") });
  console.log("🔧 Loaded .env file for local development");
} else {
  // Debug logging for Railway deployment
  logger.info("=== RAILWAY DEPLOYMENT DEBUG ===");
  logger.info({ env: process.env.NODE_ENV }, "NODE_ENV");
  logger.info({ isRailway: isRailwayDeployment }, "Railway Environment");
  logger.info(
    {
      hasDbUrl: !!process.env.DATABASE_URL,
      len: process.env.DATABASE_URL?.length || 0,
    },
    "DATABASE_URL exists/length"
  );

  // Log all environment variables that contain 'DATABASE' (case insensitive)
  const dbVars = Object.keys(process.env).filter(
    (key) =>
      key.toLowerCase().includes("database") || key.toLowerCase().includes("db")
  );
  logger.info({ dbVars }, "Database-related env vars");

  // Log first 50 chars of DATABASE_URL if it exists (for debugging without exposing full credentials)
  if (process.env.DATABASE_URL) {
    logger.info(
      { preview: process.env.DATABASE_URL.substring(0, 50) + "..." },
      "DATABASE_URL preview"
    );
  }

  // Log Railway-specific variables
  const railwayVars = Object.keys(process.env).filter(
    (key) => key.startsWith("RAILWAY_") || key.startsWith("NIXPACKS_")
  );
  logger.info({ count: railwayVars.length }, "Railway vars found");
  logger.info("=== END RAILWAY DEBUG ===");
}

// Initialize Sentry for server error tracking
if (process.env.SENTRY_DSN) {
  Sentry.init({
    dsn: process.env.SENTRY_DSN,
    environment: process.env.NODE_ENV || "development",
    tracesSampleRate: 0.1,
  });
}

const app = express();

// Security middleware (apply early)
app.use(helmet(helmetOptions));
app.use(cors(corsOptions));
app.use(securityHeaders);
app.use(rateLimiter);
app.use(sanitizeInput);
// Compression for text/JSON responses
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: false, limit: "10mb" }));

// Mount development test fixtures early (dev only)
try {
  const { addTestFixtures } = await import("./routes/testFixtures");
  addTestFixtures(app);
  if (process.env.NODE_ENV === 'development') {
    logger.info("Mounted dev test fixtures under /api/test/*");
  }
} catch (e) {
  // ignore if module not found
}

// Audit logging
app.use(auditLogger);

app.use((req, res, next) => {
  const start = Date.now();
  const reqPath = req.path;
  const isProd = process.env.NODE_ENV === "production";
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  if (!isProd) {
    (res as any).json = function (bodyJson: any, ...args: any[]) {
      capturedJsonResponse = bodyJson;
      return originalResJson.apply(res, [bodyJson, ...args]);
    } as any;
  }

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (reqPath.startsWith("/api")) {
      let logLine = `${req.method} ${reqPath} ${res.statusCode} in ${duration}ms`;
      if (!isProd && capturedJsonResponse) {
        try {
          const snippet = JSON.stringify(capturedJsonResponse);
          if (snippet && snippet.length <= 200) {
            logLine += ` :: ${snippet}`;
          }
        } catch {}
      }
      logger.info(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);
  // Health endpoint
  addHealthRoutes(app);

  // API 404 JSON guard (before Vite catch-all)
  app.use('/api', (_req: Request, res: Response) => {
    res.status(404).json({ message: 'Not found' });
  });

  // Add Sentry error handler
  if (process.env.SENTRY_DSN) {
    Sentry.setupExpressErrorHandler(app);
  }

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes

  // Check if we're in Replit deployment
  const isReplitDeployment = process.env.REPL_DEPLOYMENT === "1";
  const isDevelopment = !isReplitDeployment && app.get("env") === "development";

  const testMode = process.env.BIDAIBLE_TEST === '1';
  if (isDevelopment && !testMode) {
    await setupVite(app, server);
  } else if (!isDevelopment) {
    logger.info("Serving static files in production mode");
    serveStatic(app);
  } else {
    logger.info("Test mode active: skipping Vite middleware to isolate API routes");
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = Number(process.env.PORT) || 5000;
  const host = process.env.NODE_ENV === "production" ? "0.0.0.0" : "localhost";

  server.listen(port, host, () => {
    logger.info(`serving on ${host}:${port}`);

    // Initialize automatic backups in production
    if (process.env.NODE_ENV === "production") {
      scheduleAutomaticBackups();
    }

    // Start the scheduled notification processor
    scheduledNotificationProcessor.start();
  });

  // Debug route to list available assets
  app.get("/api/debug/assets", (req, res) => {
    const fs = require("fs");
    const assetsPath = path.join(__dirname, "..", "attached_assets");
    try {
      const files = fs.readdirSync(assetsPath);
      const imageFiles = files.filter(
        (file: string) =>
          file.endsWith(".png") ||
          file.endsWith(".jpg") ||
          file.endsWith(".jpeg")
      );
      res.json({
        assetsPath,
        totalFiles: files.length,
        imageFiles: imageFiles.slice(0, 10), // First 10 images
      });
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });
})();
