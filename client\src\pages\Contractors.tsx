import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { ContractorProfileForm } from "@/components/ContractorProfileForm";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Plus, Search, Filter, MapPin, Calendar, Check, X, Star, Shield, Phone, Mail, Building2, Users, Award, FileText } from "lucide-react";
import { format } from "date-fns";

// Mock contractor data
const mockContractors = [
  {
    id: "contractor-001",
    companyName: "Elite Electrical Solutions",
    businessAddress: "1234 Industrial Blvd, Seattle, WA 98101",
    primaryContact: "<PERSON>",
    contactPhone: "(*************",
    contactEmail: "<EMAIL>",
    licenseNumber: "ELEC-2024-001",
    insuranceAmount: 2000000,
    bondingCapacity: 5000000,
    yearsInBusiness: 15,
    numberOfEmployees: 45,
    tradeTypes: ["electrical"],
    classification: "Prime Contractor",
    unionStatus: "Union",
    isApproved: true,
    rating: 4.8,
    completedProjects: 187,
    certifications: ["NECA Certified", "IBEW Local 46", "OSHA 30"],
    specializations: ["Commercial Wiring", "Industrial Controls", "Solar Installation"],
    workLocation: "Greater Seattle Area",
    emergencyAvailable: true,
    prequalificationLevel: "A+",
    createdAt: "2023-08-15T10:30:00Z"
  },
  {
    id: "contractor-002",
    companyName: "Precision Plumbing & Mechanical",
    businessAddress: "5678 Commerce Way, Bellevue, WA 98004",
    primaryContact: "Sarah Chen",
    contactPhone: "(*************",
    contactEmail: "<EMAIL>",
    licenseNumber: "PLUM-2024-002",
    insuranceAmount: 1500000,
    bondingCapacity: 3000000,
    yearsInBusiness: 12,
    numberOfEmployees: 28,
    tradeTypes: ["plumbing", "hvac"],
    classification: "Specialty Contractor",
    unionStatus: "Non-Union",
    isApproved: true,
    rating: 4.6,
    completedProjects: 143,
    certifications: ["WA State Master Plumber", "EPA 608 Certified", "NATE Certified"],
    specializations: ["Medical Gas Systems", "Hydronic Heating", "Green Plumbing"],
    workLocation: "King County & Eastside",
    emergencyAvailable: true,
    prequalificationLevel: "A",
    createdAt: "2023-09-22T14:15:00Z"
  },
  {
    id: "contractor-003",
    companyName: "Northwest Concrete & Construction",
    businessAddress: "9012 Industrial Park Dr, Tacoma, WA 98402",
    primaryContact: "Robert Johnson",
    contactPhone: "(*************",
    contactEmail: "<EMAIL>",
    licenseNumber: "CONC-2024-003",
    insuranceAmount: 5000000,
    bondingCapacity: 10000000,
    yearsInBusiness: 25,
    numberOfEmployees: 85,
    tradeTypes: ["concrete", "general"],
    classification: "General Contractor",
    unionStatus: "Union",
    isApproved: false,
    rating: 4.9,
    completedProjects: 312,
    certifications: ["ACI Certified", "AGC Member", "Laborers Local 242"],
    specializations: ["Structural Concrete", "Decorative Concrete", "Site Work"],
    workLocation: "Puget Sound Region",
    emergencyAvailable: false,
    prequalificationLevel: "A+",
    createdAt: "2024-01-10T09:45:00Z"
  }
];

export default function Contractors() {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [tradeFilter, setTradeFilter] = useState("all");
  const [selectedContractor, setSelectedContractor] = useState<any | null>(null);

  // Fetch real contractors from API
  const { data: contractors = [], isLoading } = useQuery<any[]>({
    queryKey: ['/api/contractors'],
  });
  const { toast } = useToast();

  const handleApproveContractor = (contractorId: string) => {
    const contractor = contractors.find(c => c.id === contractorId);
    // TODO: Implement actual API call to approve contractor
    toast({
      title: "Contractor Approved",
      description: `${contractor?.companyName} has been successfully approved and can now bid on projects.`,
    });
  };

  const handleRejectContractor = (contractorId: string) => {
    const contractor = contractors.find(c => c.id === contractorId);
    // TODO: Implement actual API call to reject contractor
    setSelectedContractor(null);
    toast({
      title: "Contractor Rejected",
      description: `${contractor?.companyName} application has been rejected and removed from the system.`,
      variant: "destructive",
    });
  };

  const handleRequestMoreInfo = (contractorId: string) => {
    // In a real app, this would send an email or notification
    toast({
      title: "Information Request Sent",
      description: `${selectedContractor?.companyName} will be notified to provide additional documentation.`,
    });
  };

  const filteredContractors = contractors.filter((contractor: any) => {
    const matchesSearch = contractor.companyName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contractor.primaryAddress?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contractor.primaryContactName?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || 
                         (statusFilter === "approved" && contractor.isApproved) ||
                         (statusFilter === "pending" && !contractor.isApproved);
    const matchesTrade = tradeFilter === "all" || 
                        contractor.tradeTypes?.includes(tradeFilter);
    
    return matchesSearch && matchesStatus && matchesTrade;
  });

  const getStatusColor = (isApproved: boolean) => {
    return isApproved
      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
      : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";
  };

  const getTradeColor = (trade: string) => {
    switch (trade) {
      case "electrical":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";
      case "plumbing":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
      case "hvac":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
      case "concrete":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
      default:
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Contractors</h1>
          <p className="text-muted-foreground">
            Manage contractor profiles and qualifications
          </p>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Contractor
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search contractors..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
          </SelectContent>
        </Select>
        <Select value={tradeFilter} onValueChange={setTradeFilter}>
          <SelectTrigger className="w-[180px]">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Filter by trade" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Trades</SelectItem>
            <SelectItem value="electrical">Electrical</SelectItem>
            <SelectItem value="plumbing">Plumbing</SelectItem>
            <SelectItem value="hvac">HVAC</SelectItem>
            <SelectItem value="concrete">Concrete</SelectItem>
            <SelectItem value="general">General</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Contractors Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-muted rounded"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : filteredContractors.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <p className="text-muted-foreground">No contractors found matching your criteria.</p>
          </div>
        ) : (
          filteredContractors.map((contractor: any) => (
            <Card key={contractor.id} className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => setSelectedContractor(contractor)}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{contractor.companyName}</CardTitle>
                    <p className="text-sm text-muted-foreground flex items-center gap-1 mt-1">
                      <MapPin className="h-3 w-3" />
                      {contractor.primaryAddress || contractor.businessAddress || contractor.workLocation || 'Location not specified'}
                    </p>
                  </div>
                  <Badge 
                    variant="secondary" 
                    className={getStatusColor(contractor.isApproved)}
                  >
                    {contractor.isApproved ? (
                      <>
                        <Check className="mr-1 h-3 w-3" />
                        Approved
                      </>
                    ) : (
                      <>
                        <X className="mr-1 h-3 w-3" />
                        Pending
                      </>
                    )}
                  </Badge>
                </div>
                
                {/* Rating */}
                <div className="flex items-center gap-1 mt-2">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm font-medium">{contractor.rating || 'N/A'}</span>
                  <span className="text-sm text-muted-foreground">({contractor.completedProjects || 0} projects)</span>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Classification and Union Status Pills */}
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800">
                      <Building2 className="h-3 w-3 mr-1" />
                      {contractor.classification || 'Contractor'}
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className={contractor.unionStatus === "Union" 
                        ? "bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800"
                        : "bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-800"
                      }
                    >
                      <Users className="h-3 w-3 mr-1" />
                      {contractor.unionStatus || 'Non-Union'}
                    </Badge>
                  </div>

                  {/* Trade Types */}
                  <div className="flex flex-wrap gap-1">
                    {contractor.tradeTypes?.map((trade: string) => (
                      <Badge 
                        key={trade} 
                        variant="secondary" 
                        className={getTradeColor(trade)}
                      >
                        {trade.charAt(0).toUpperCase() + trade.slice(1)}
                      </Badge>
                    ))}
                  </div>
                  
                  {/* Key Info */}
                  <div className="text-sm space-y-2">
                    <div className="flex items-center gap-2">
                      <Phone className="h-3 w-3 text-muted-foreground" />
                      <span>{contractor.primaryContactPhone || contractor.contactPhone || 'Phone not listed'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Mail className="h-3 w-3 text-muted-foreground" />
                      <span className="truncate">{contractor.primaryContactEmail || contractor.contactEmail || 'Email not listed'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Shield className="h-3 w-3 text-muted-foreground" />
                      <span>License: {contractor.licenseNumber || 'Not specified'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Award className="h-3 w-3 text-muted-foreground" />
                      <span>Level: {contractor.prequalificationLevel || 'Not rated'}</span>
                    </div>
                  </div>

                  {/* Key Stats */}
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">Experience:</span>
                      <p className="font-medium">{contractor.yearsInBusiness} years</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Employees:</span>
                      <p className="font-medium">{contractor.numberOfEmployees}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Contractor Detail Modal */}
      <Dialog open={selectedContractor !== null} onOpenChange={() => setSelectedContractor(null)}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Contractor Profile - {selectedContractor?.companyName}</DialogTitle>
          </DialogHeader>
          {selectedContractor && (
            <div className="space-y-6">
              {/* Header Section */}
              <div className="flex items-start justify-between">
                <div>
                  <h2 className="text-2xl font-bold">{selectedContractor.companyName}</h2>
                  <p className="text-muted-foreground">{selectedContractor.primaryAddress || selectedContractor.businessAddress}</p>
                  <div className="flex items-center gap-2 mt-2">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium">{selectedContractor.rating || 'N/A'}/5.0</span>
                    <span className="text-muted-foreground">({selectedContractor.completedProjects || 0} completed projects)</span>
                  </div>
                </div>
                <Badge 
                  variant="secondary" 
                  className={getStatusColor(selectedContractor.isApproved)}
                >
                  {selectedContractor.isApproved ? "Approved" : "Pending Approval"}
                </Badge>
              </div>

              {/* Classification Tags */}
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800">
                  <Building2 className="h-3 w-3 mr-1" />
                  {selectedContractor.classification}
                </Badge>
                <Badge 
                  variant="outline" 
                  className={selectedContractor.unionStatus === "Union" 
                    ? "bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800"
                    : "bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-800"
                  }
                >
                  <Users className="h-3 w-3 mr-1" />
                  {selectedContractor.unionStatus}
                </Badge>
                <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300 dark:border-purple-800">
                  <Award className="h-3 w-3 mr-1" />
                  Level {selectedContractor.prequalificationLevel}
                </Badge>
              </div>

              {/* Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-2">Contact Information</h3>
                    <div className="space-y-1 text-sm">
                      <p><strong>Primary Contact:</strong> {selectedContractor.primaryContactName || selectedContractor.primaryContact}</p>
                      <p><strong>Phone:</strong> {selectedContractor.primaryContactPhone || selectedContractor.contactPhone}</p>
                      <p><strong>Email:</strong> {selectedContractor.primaryContactEmail || selectedContractor.contactEmail}</p>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">Trade Types</h3>
                    <div className="flex flex-wrap gap-1">
                      {selectedContractor.tradeTypes.map((trade: string) => (
                        <Badge key={trade} variant="secondary" className={getTradeColor(trade)}>
                          {trade.charAt(0).toUpperCase() + trade.slice(1)}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">Certifications</h3>
                    <div className="flex flex-wrap gap-1">
                      {selectedContractor.certifications.map((cert: string, index: number) => (
                        <Badge key={index} variant="outline" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800">
                          <FileText className="h-3 w-3 mr-1" />
                          {cert}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-2">Company Details</h3>
                    <div className="space-y-1 text-sm">
                      <p><strong>Years in Business:</strong> {selectedContractor.yearsInBusiness}</p>
                      <p><strong>Number of Employees:</strong> {selectedContractor.numberOfEmployees}</p>
                      <p><strong>License Number:</strong> {selectedContractor.licenseNumber}</p>
                      <p><strong>Work Location:</strong> {selectedContractor.workLocation}</p>
                      {selectedContractor.companyWebsite && (
                        <p><strong>Website:</strong> <a href={selectedContractor.companyWebsite} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">{selectedContractor.companyWebsite}</a></p>
                      )}
                      <p><strong>Emergency Services:</strong> {selectedContractor.emergencyAvailable ? "Available" : "Not Available"}</p>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">Financial Information</h3>
                    <div className="space-y-1 text-sm">
                      <p><strong>Insurance Coverage:</strong> ${(selectedContractor.insuranceAmount / 1000000).toFixed(1)}M</p>
                      <p><strong>Bonding Capacity:</strong> ${(selectedContractor.bondingCapacity / 1000000).toFixed(1)}M</p>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">Specializations</h3>
                    <div className="flex flex-wrap gap-1">
                      {selectedContractor.specializations.map((spec: string, index: number) => (
                        <Badge key={index} variant="outline">
                          {spec}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              {!selectedContractor.isApproved && (
                <div className="flex gap-2 pt-4 border-t">
                  <Button 
                    className="flex-1"
                    onClick={() => handleApproveContractor(selectedContractor.id)}
                  >
                    <Check className="h-4 w-4 mr-2" />
                    Approve Contractor
                  </Button>
                  <Button 
                    variant="outline" 
                    className="flex-1"
                    onClick={() => handleRequestMoreInfo(selectedContractor.id)}
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    Request More Info
                  </Button>
                  <Button 
                    variant="destructive" 
                    className="flex-1"
                    onClick={() => {
                      if (confirm(`Are you sure you want to reject ${selectedContractor.companyName}? This action cannot be undone.`)) {
                        handleRejectContractor(selectedContractor.id);
                      }
                    }}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Reject Application
                  </Button>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Create Contractor Modal */}
      <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Contractor</DialogTitle>
            <DialogDescription>
              Complete the contractor profile form to add a new contractor to the system.
            </DialogDescription>
          </DialogHeader>
          <ContractorProfileForm />
        </DialogContent>
      </Dialog>
    </div>
  );
}
