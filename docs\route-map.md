# Route Map

## Core API Routes

### Admin Routes
| Method | Path | File | Description |
|---|---|---|---|
| POST | /api/admin/api-keys/rotate | server\routes.ts | Rotate API keys |
| GET | /api/admin/api-keys/rotation-needed | server\routes.ts | Check if API keys need rotation |
| GET | /api/admin/api-keys/security-stats | server\routes.ts | Get API key security statistics |
| GET | /api/admin/audit/access | server\routes.ts | Get access audit logs |
| GET | /api/admin/audit/business | server\routes.ts | Get business audit logs |
| POST | /api/admin/audit/business/backfill | server\routes.ts | Backfill business audit logs |
| GET | /api/admin/audit/roles | server\routes.ts | Get role audit logs |
| POST | /api/admin/backup/create | server\routes.ts | Create system backup |
| GET | /api/admin/backup/stats | server\routes.ts | Get backup statistics |
| POST | /api/admin/cache/clear | server\routes.ts | Clear system cache |
| GET | /api/admin/performance/cache | server\routes.ts | Get cache performance metrics |
| GET | /api/admin/performance/files | server\routes.ts | Get file processing performance |
| GET | /api/admin/security/audit | server\routes.ts | Get security audit information |
| POST | /api/admin/security/invalidate-sessions | server\routes.ts | Invalidate user sessions |
| GET | /api/admin/users | server\routes.ts | Get all users (admin view) |
| PATCH | /api/admin/users/:userId/role | server\routes.ts | Update user role |

### Authentication & User Routes
| Method | Path | File | Description |
|---|---|---|---|
| GET | /api/auth/api-keys | server\routes.ts | Get user API keys |
| POST | /api/auth/api-keys | server\routes.ts | Create new API key |
| DELETE | /api/auth/api-keys/:id | server\routes.ts | Delete API key |
| PATCH | /api/auth/api-keys/:id | server\routes.ts | Update API key |
| GET | /api/auth/api-keys/:id/stats | server\routes.ts | Get API key usage stats |
| GET | /api/auth/user | server\routes.ts | Get current user |
| PATCH | /api/auth/user | server\routes.ts | Update user profile |
| PATCH | /api/clerk/accept-terms | server\routes.ts | Accept terms and conditions |
| GET | /api/clerk/user | server\routes.ts | Get Clerk user information |
| GET | /api/logout | server\routes.ts | User logout |

### RFQ Routes
| Method | Path | File | Description |
|---|---|---|---|
| GET | /api/rfqs | server\routes.ts | Get all RFQs |
| POST | /api/rfqs | server\routes.ts | Create new RFQ with document upload |
| GET | /api/rfqs/:id | server\routes.ts | Get specific RFQ |
| PUT | /api/rfqs/:id | server\routes.ts | Update RFQ |
| GET | /api/rfqs/:id/documents | server\routes.ts | Get RFQ documents |
| POST | /api/rfqs/process-documents | server\routes.ts | Process RFQ documents |
| GET | /api/rfqs/:rfqId/bid-comparison | server\routes.ts | Get bid comparison data |
| GET | /api/rfqs/:rfqId/bid-comparison/export | server\routes.ts | Export bid comparison |
| GET | /api/rfqs/:rfqId/bids | server\routes.ts | Get RFQ bids |
| POST | /api/rfqs/:rfqId/bids | server\routes.ts | Submit bid to RFQ |
| GET | /api/rfqs/:rfqId/bids/analysis | server\routes.ts | Get bid analysis |
| PUT | /api/rfqs/:rfqId/buffer | server\routes.ts | Update RFQ buffer |
| GET | /api/rfqs/:rfqId/category-analysis | server\routes.ts | Get category analysis |
| GET | /api/rfqs/:rfqId/comprehensive-bid-analysis | server\routes.ts | Get comprehensive bid analysis |
| PATCH | /api/rfqs/:rfqId/decline | server\routes.ts | Decline RFQ |
| POST | /api/rfqs/:rfqId/distribute | server\routes.ts | Distribute RFQ |
| GET | /api/rfqs/:rfqId/distributions | server\routes.ts | Get RFQ distributions |
| POST | /api/rfqs/:rfqId/generate-master-summary | server\routes.ts | Generate master summary |
| PATCH | /api/rfqs/:rfqId/view | server\routes.ts | Mark RFQ as viewed |
| PATCH | /api/rfqs/:id/archive | server\routes.ts | Archive RFQ |
| PATCH | /api/rfqs/bulk-archive | server\routes.ts | Bulk archive RFQs |
| PATCH | /api/rfqs/:id/unarchive | server\routes.ts | Unarchive RFQ |
| GET | /api/rfqs/archived | server\routes.ts | Get archived RFQs |
| GET | /api/rfqs/:id/archive-download | server\routes.ts | Download RFQ archive |

### Bid Routes
| Method | Path | File | Description |
|---|---|---|---|
| GET | /api/bids/:bidId | server\routes.ts | Get specific bid |
| PATCH | /api/bids/:bidId | server\routes.ts | Update bid |
| POST | /api/bids/:bidId/action | server\routes.ts | Perform bid action |
| POST | /api/bids/:bidId/analyze | server\routes.ts | Analyze bid |
| GET | /api/bids/:bidId/documents | server\routes.ts | Get bid documents |
| GET | /api/bids/:bidId/structured-data | server\routes.ts | Get bid structured data |

### Contractor Routes
| Method | Path | File | Description |
|---|---|---|---|
| GET | /api/contractors | server\routes.ts | Get all contractors |
| POST | /api/contractors | server\routes.ts | Create contractor |
| PATCH | /api/contractors/:id | server\routes.ts | Update contractor |
| DELETE | /api/contractors/:contractorId/favorite | server\routes.ts | Remove from favorites |
| POST | /api/contractors/:contractorId/favorite | server\routes.ts | Add to favorites |
| GET | /api/contractors/bids | server\routes.ts | Get contractor bids |
| GET | /api/contractors/favorites | server\routes.ts | Get favorite contractors |
| GET | /api/contractors/performance-stats | server\routes.ts | Get contractor performance |
| GET | /api/contractors/profile | server\routes.ts | Get contractor profile |
| PUT | /api/contractors/profile | server\routes.ts | Update contractor profile |
| GET | /api/contractors/rfqs | server\routes.ts | Get contractor RFQs |
| GET | /api/contractors/rfqs/all | server\routes.ts | Get all contractor RFQs |

### Organization Routes
| Method | Path | File | Description |
|---|---|---|---|
| POST | /api/organizations | server\routes.ts | Create organization |
| GET | /api/organizations/:orgId/invitations | server\routes.ts | Get organization invitations |
| POST | /api/organizations/:orgId/invitations | server\routes.ts | Create organization invitation |
| GET | /api/organizations/:orgId/users | server\routes.ts | Get organization users |
| DELETE | /api/organizations/:orgId/users/:targetUserId | server\routes.ts | Remove user from organization |
| PATCH | /api/organizations/:orgId/users/:targetUserId/role | server\routes.ts | Update user role in organization |

### Notification Routes
| Method | Path | File | Description |
|---|---|---|---|
| GET | /api/notifications | server\routes.ts | Get notifications |
| PATCH | /api/notifications/:id/read | server\routes.ts | Mark notification as read |
| GET | /api/notifications/preferences | server\routes.ts | Get notification preferences |
| PUT | /api/notifications/preferences/:type | server\routes.ts | Update notification preferences |
| POST | /api/notifications/send-custom-email | server\routes.ts | Send custom email |
| POST | /api/notifications/test | server\routes.ts | Test notifications |
| GET | /api/notifications/unread | server\routes.ts | Get unread notifications |

### File & Upload Routes
| Method | Path | File | Description |
|---|---|---|---|
| GET | /api/files/:documentId | server\routes.ts | Get/download file |
| POST | /api/upload/start-session | server\routes.ts | Start upload session |
| GET | /api/upload/progress/:sessionId | server\routes.ts | Get upload progress |
| GET | /attached_assets/:fileName | server\routes.ts | Get attached assets |

### Analytics Routes
| Method | Path | File | Description |
|---|---|---|---|
| GET | /api/analytics/competitive-intelligence/:rfqId | server\routes\analytics.ts | Get competitive intelligence |
| GET | /api/analytics/risk-assessment/:bidId | server\routes\analytics.ts | Get risk assessment |
| GET | /api/analytics/predictive-analytics/:rfqId | server\routes\analytics.ts | Get predictive analytics |
| POST | /api/analytics/evaluation-scores/:rfqId | server\routes\analytics.ts | Post evaluation scores |
| GET | /api/analytics/bid-analysis/:rfqId | server\routes\analytics.ts | Get bid analysis |
| GET | /api/analytics/market-intelligence/:region/:trade | server\routes\analytics.ts | Get market intelligence |
| GET | /api/analytics/default-criteria | server\routes\analytics.ts | Get default criteria |

### Dashboard & Statistics Routes
| Method | Path | File | Description |
|---|---|---|---|
| GET | /api/dashboard/stats | server\routes.ts | Get dashboard statistics |
| GET | /api/processing/stats | server\routes\processingStats.ts | Get processing statistics |
| GET | /api/user/activity | server\routes.ts | Get user activity |

### Integration Routes
| Method | Path | File | Description |
|---|---|---|---|
| GET | /api/integrations/export/rfqs | server\routes.ts | Export RFQs |
| GET | /api/integrations/project-budget/:rfqId/:bidId | server\routes.ts | Get project budget |
| GET | /api/integrations/quickbooks | server\routes.ts | QuickBooks integration |
| POST | /api/integrations/sage | server\routes.ts | Sage integration |

### Forecast & Materials Routes
| Method | Path | File | Description |
|---|---|---|---|
| GET | /api/forecast/materials | server\routes.ts | Get forecast materials |
| POST | /api/forecast/materials | server\routes.ts | Create forecast materials |
| POST | /api/forecast/search | server\routes.ts | Search forecast data |

### Reports & Feedback Routes
| Method | Path | File | Description |
|---|---|---|---|
| POST | /api/reports/email | server\routes.ts | Send email report |
| GET | /api/user-feedback | server\routes.ts | Get user feedback |
| POST | /api/user-feedback | server\routes.ts | Submit user feedback |
| PATCH | /api/user-feedback/:id | server\routes.ts | Update user feedback |
| GET | /api/user-feedback/stats | server\routes.ts | Get feedback statistics |

### Utility Routes
| Method | Path | File | Description |
|---|---|---|---|
| GET | /api/health | server\routes\health.ts | Health check endpoint |
| POST | /api/waitlist | server\routes.ts | Add to waitlist |
| GET | /api/waitlist/count | server\routes.ts | Get waitlist count |
| GET | /env | server\index.ts | Environment information |
| GET | /api/debug/assets | server\index.ts | Debug assets (development) |

## Development & Test Routes (NODE_ENV=development only)

### Test Fixtures
| Method | Path | File | Description |
|---|---|---|---|
| POST | /api/test/org | server\routes\testFixtures.ts | Create test organization |
| POST | /api/test/user | server\routes\testFixtures.ts | Create test user |
| POST | /api/test/contractor | server\routes\testFixtures.ts | Create test contractor |
| POST | /api/test/rfq-simple | server\routes\testFixtures.ts | Create simple test RFQ |
| POST | /api/test/rfq | server\routes\testFixtures.ts | Create test RFQ |
| POST | /api/test/rfq-document | server\routes\testFixtures.ts | Create test RFQ document |
| POST | /api/test/distribute | server\routes\testFixtures.ts | Test distribution |
| POST | /api/test/api-key | server\routes\testFixtures.ts | Create test API key |
| GET | /api/test/whoami | server\routes\testFixtures.ts | Get current user info |
| GET | /api/test/ping | server\routes\testFixtures.ts | Test endpoint |

### Additional Test Routes
| Method | Path | File | Description |
|---|---|---|---|
| POST | /api/test/rfqs | server\routes.ts | Create test RFQs (bypasses auth) |
| POST | /api/test/github-feedback | server\routes.ts | Test GitHub feedback |
| GET | /api/test/unified-pdf-extractor | server\routes\testUnifiedExtractor.ts | Test PDF extraction |

## Route File Organization

- **server\routes.ts** - Main route definitions (core business logic)
- **server\routes\analytics.ts** - Advanced analytics and intelligence APIs
- **server\routes\health.ts** - System health monitoring
- **server\routes\processingStats.ts** - File processing statistics
- **server\routes\testFixtures.ts** - Development test data creation (NODE_ENV=development only)
- **server\routes\testUnifiedExtractor.ts** - PDF extraction testing
- **server\index.ts** - Basic utility and debug routes

## Authentication Middleware

- **protectedRoute** - Requires Clerk authentication
- **addUserToRequest** - Adds user context to request
- **readOnlyApiAccess** - API key based read-only access
- **orgAuth** - Organization-based authorization

## Environment Dependencies

- **Development Only**: All `/api/test/*` routes are disabled in production
- **Production**: PDF processing uses pdf-parse only (PDF.js disabled)
- **Railway Deployment**: Uses NODE_ENV=production with specific configurations

---

**Last Updated**: August 26, 2025  
**Total Routes**: 80+ endpoints across multiple route files
