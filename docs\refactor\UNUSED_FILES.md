# Unused Files Analysis

This document contains the analysis of unused exports and files identified by ts-prune. These items are candidates for cleanup and refactoring.

## Overview

The following analysis is based on ts-prune output and identifies:

- Unused exports that are marked as "(used in module)" - these are exported but only used internally
- Standalone unused exports
- Files organized by functionality

## Server-Side Unused Exports

### Database & Storage

| File                          | Line | Export                        | Status         |
| ----------------------------- | ---- | ----------------------------- | -------------- |
| `server/db.ts`                | 85   | `pool`                        | used in module |
| `server/storage-optimized.ts` | 12   | `getRfqsWithBidsAndLineItems` | unused         |
| `server/storage-optimized.ts` | 99   | `getBidsWithLineItems`        | unused         |
| `server/storage-optimized.ts` | 162  | `getUsersByOrganizations`     | unused         |
| `server/storage-optimized.ts` | 191  | `getUsersWithOrganizations`   | unused         |
| `server/storage.ts`           | 68   | `IStorage`                    | used in module |
| `server/storage.ts`           | 221  | `DatabaseStorage`             | used in module |

### Middleware & Security

| File                            | Line | Export                       | Status         |
| ------------------------------- | ---- | ---------------------------- | -------------- |
| `server/middleware/apiAuth.ts`  | 89   | `createPermissionMiddleware` | used in module |
| `server/middleware/apiAuth.ts`  | 122  | `combinedAuth`               | used in module |
| `server/middleware/apiAuth.ts`  | 175  | `fullApiAccess`              | unused         |
| `server/middleware/security.ts` | 174  | `additionalSecurityHeaders`  | used in module |
| `server/middleware/security.ts` | 191  | `securityLogger`             | used in module |
| `server/middleware/security.ts` | 231  | `securityRateLimit`          | used in module |
| `server/middleware/security.ts` | 274  | `createSimpleRateLimiter`    | used in module |

### Services

| File                                      | Line | Export                    | Status         |
| ----------------------------------------- | ---- | ------------------------- | -------------- |
| `server/services/aiStreamProcessor.ts`    | 22   | `AIStreamProcessor`       | used in module |
| `server/services/apiKeyService.ts`        | 249  | `revokeApiKey`            | unused         |
| `server/services/apiKeyService.ts`        | 301  | `resetFailedAttempts`     | used in module |
| `server/services/apiKeyService.ts`        | 440  | `updateApiKeySettings`    | unused         |
| `server/services/apiKeyService.ts`        | 18   | `ApiKeyData`              | used in module |
| `server/services/apiKeyService.ts`        | 32   | `ApiKeySecurityInfo`      | used in module |
| `server/services/backupService.ts`        | 187  | `createFullBackup`        | unused         |
| `server/services/backupService.ts`        | 208  | `listBackups`             | used in module |
| `server/services/backupService.ts`        | 245  | `cleanupOldBackups`       | used in module |
| `server/services/backupService.ts`        | 270  | `restoreDatabase`         | unused         |
| `server/services/enhancedBidAnalytics.ts` | 640  | `saveRiskAssessment`      | unused         |
| `server/services/enhancedBidAnalytics.ts` | 17   | `CompetitiveIntelligence` | used in module |
| `server/services/enhancedBidAnalytics.ts` | 38   | `EnhancedRiskAssessment`  | used in module |
| `server/services/enhancedBidAnalytics.ts` | 65   | `PredictiveAnalytics`     | used in module |

### File Processing & Storage

| File                                       | Line | Export                    | Status         |
| ------------------------------------------ | ---- | ------------------------- | -------------- |
| `server/services/fileProcessingService.ts` | 122  | `getUploadMiddleware`     | unused         |
| `server/services/fileProcessingService.ts` | 136  | `validateFileContent`     | used in module |
| `server/services/fileProcessingService.ts` | 176  | `compressFileIfNeeded`    | unused         |
| `server/services/fileProcessingService.ts` | 212  | `processLargeFile`        | unused         |
| `server/services/fileProcessingService.ts` | 240  | `extractFileMetadata`     | unused         |
| `server/services/fileProcessingService.ts` | 260  | `calculateBufferChecksum` | used in module |
| `server/services/fileProcessingService.ts` | 268  | `optimizeForStorage`      | unused         |
| `server/services/objectStorageService.ts`  | 249  | `deleteFile`              | unused         |
| `server/services/objectStorageService.ts`  | 266  | `fileExists`              | unused         |
| `server/services/objectStorageService.ts`  | 285  | `getPresignedUrl`         | unused         |
| `server/services/objectStorageService.ts`  | 302  | `listFiles`               | unused         |
| `server/services/objectStorageService.ts`  | 18   | `StoredFile`              | used in module |
| `server/services/objectStorageService.ts`  | 37   | `ChunkedUploadOptions`    | used in module |

### Audit & Notifications

| File                                                | Line | Export                           | Status         |
| --------------------------------------------------- | ---- | -------------------------------- | -------------- |
| `server/services/auditService.ts`                   | 6    | `FileUploadEventData`            | used in module |
| `server/services/auditService.ts`                   | 15   | `BidActionEventData`             | used in module |
| `server/services/auditService.ts`                   | 25   | `RfqCreationEventData`           | used in module |
| `server/services/auditService.ts`                   | 32   | `RfqDistributionEventData`       | used in module |
| `server/services/scheduledNotificationProcessor.ts` | 10   | `ScheduledNotificationProcessor` | used in module |

### Core Services

| File                                       | Line | Export                    | Status         |
| ------------------------------------------ | ---- | ------------------------- | -------------- |
| `server/services/core/pdfExtractor.ts`     | 60   | `UnifiedPDFExtractor`     | used in module |
| `server/services/core/testPdfExtractor.ts` | 10   | `testUnifiedPDFExtractor` | used in module |
| `server/services/core/testPdfExtractor.ts` | 81   | `runPDFExtractionTest`    | unused         |

### Utilities

| File                     | Line | Export          | Status         |
| ------------------------ | ---- | --------------- | -------------- |
| `server/utils/typing.ts` | 8    | `asUpdate`      | unused         |
| `server/utils/typing.ts` | 13   | `withUpdatedAt` | unused         |
| `server/vite.ts`         | 16   | `log`           | used in module |

## Client-Side Unused Exports

### Components

| File                                                 | Line | Export                   | Status         |
| ---------------------------------------------------- | ---- | ------------------------ | -------------- |
| `client/src/components/AdvancedBidFilters.tsx`       | 62   | `default`                | unused         |
| `client/src/components/AdvancedBidFilters.tsx`       | 14   | `AdvancedFilterState`    | used in module |
| `client/src/components/BidLineItemForm.tsx`          | 12   | `BidLineItem`            | used in module |
| `client/src/components/BidManagementWidget.tsx`      | 62   | `BidManagementWidget`    | unused         |
| `client/src/components/BidScopeDefinition.tsx`       | 13   | `ScopeItem`              | unused         |
| `client/src/components/CategoryComparisonView.tsx`   | 94   | `CategoryComparisonView` | unused         |
| `client/src/components/MultiFileUploadInterface.tsx` | 9    | `FileType`               | used in module |
| `client/src/components/PDFViewer.tsx`                | 17   | `PDFViewer`              | unused         |

### Hooks & Utilities

| File                                       | Line | Export                  | Status         |
| ------------------------------------------ | ---- | ----------------------- | -------------- |
| `client/src/hooks/use-toast.ts`            | 74   | `reducer`               | used in module |
| `client/src/hooks/useNotifications.ts`     | 21   | `NotificationsResponse` | used in module |
| `client/src/hooks/useStructuredBidData.ts` | 65   | `calculateDataQuality`  | unused         |
| `client/src/hooks/useStructuredBidData.ts` | 25   | `StructuredBidData`     | used in module |
| `client/src/hooks/useUserRole.ts`          | 5    | `UserRoleType`          | used in module |
| `client/src/lib/authUtils.ts`              | 1    | `isUnauthorizedError`   | unused         |

### Constants & Types

| File                                     | Line | Export                 | Status         |
| ---------------------------------------- | ---- | ---------------------- | -------------- |
| `client/src/constants/certifications.ts` | 1    | `CertificationOption`  | used in module |
| `client/src/constants/trades.ts`         | 1    | `TradeOption`          | used in module |
| `client/src/types/notifications.ts`      | 2    | `NotificationTypes`    | used in module |
| `client/src/types/notifications.ts`      | 26   | `NotificationType`     | unused         |
| `client/src/types/notifications.ts`      | 28   | `NotificationPriority` | unused         |
| `client/src/types/notifications.ts`      | 30   | `NotificationData`     | unused         |

### Providers

| File                                     | Line | Export     | Status |
| ---------------------------------------- | ---- | ---------- | ------ |
| `client/src/providers/TermsProvider.tsx` | 14   | `useTerms` | unused |

### UI Components (Extensive List)

The following UI components appear to be unused or only used internally. These are likely part of a component library:

#### Alert & Dialog Components

- `AlertDialogPortal`, `AlertDialogOverlay`, `AlertDialogAction`, `AlertDialogCancel` - used in module
- `AlertTitle` - used in module

#### Form & Input Components

- `AspectRatio`, `Badge`, `BadgeProps`, `badgeVariants` - used in module
- `InputOTP`, `InputOTPGroup`, `InputOTPSlot`, `InputOTPSeparator` - used in module

#### Navigation & Layout Components

- `Breadcrumb`, `BreadcrumbList`, `BreadcrumbItem`, `BreadcrumbLink`, `BreadcrumbPage`, `BreadcrumbSeparator`, `BreadcrumbEllipsis` - used in module
- `Sidebar` and related components (extensive list) - used in module

#### Data Display Components

- `Calendar`, `Card`, `Carousel`, `Chart`, `Table` components - used in module
- `Command`, `ContextMenu`, `Dialog`, `Drawer` components - used in module

## Recommendations

### High Priority for Removal

1. **Completely Unused Exports** - These can be safely removed:
   - `server/services/apiKeyService.ts`: `revokeApiKey`, `updateApiKeySettings`
   - `server/services/backupService.ts`: `createFullBackup`, `restoreDatabase`
   - `server/services/enhancedBidAnalytics.ts`: `saveRiskAssessment`
   - `server/services/fileProcessingService.ts`: Multiple file processing functions
   - `server/services/objectStorageService.ts`: File operation functions
   - `server/utils/typing.ts`: `asUpdate`, `withUpdatedAt`

2. **Unused Components** - Review and remove if not needed:
   - `BidManagementWidget`, `BidScopeDefinition`, `CategoryComparisonView`
   - `PDFViewer`, `AdvancedBidFilters`

### Medium Priority for Review

1. **"Used in Module" Exports** - Review if these need to be exported:
   - Many middleware and service exports that are only used internally
   - Type definitions that might be over-exported

### Low Priority

1. **UI Components** - These appear to be part of a design system and should be kept unless specifically unused

## Action Items

- [ ] Remove high-priority unused exports
- [ ] Review medium-priority exports for potential un-exporting
- [ ] Update import statements after removals
- [ ] Run tests to ensure no breaking changes
- [ ] Re-run ts-prune to verify cleanup success
      This document contains the analysis of unused exports and files identified by ts-prune. These items are candidates for cleanup and refactoring.

## Overview

The following analysis is based on ts-prune output and identifies:

- Unused exports that are marked as "(used in module)" - these are exported but only used internally
- Standalone unused exports
- Files organized by functionality

## Server-Side Unused Exports

### Database & Storage

| File                          | Line | Export                        | Status         |
| ----------------------------- | ---- | ----------------------------- | -------------- |
| `server/db.ts`                | 85   | `pool`                        | used in module |
| `server/storage-optimized.ts` | 12   | `getRfqsWithBidsAndLineItems` | unused         |
| `server/storage-optimized.ts` | 99   | `getBidsWithLineItems`        | unused         |
| `server/storage-optimized.ts` | 162  | `getUsersByOrganizations`     | unused         |
| `server/storage-optimized.ts` | 191  | `getUsersWithOrganizations`   | unused         |
| `server/storage.ts`           | 68   | `IStorage`                    | used in module |
| `server/storage.ts`           | 221  | `DatabaseStorage`             | used in module |

### Middleware & Security

| File                            | Line | Export                       | Status         |
| ------------------------------- | ---- | ---------------------------- | -------------- |
| `server/middleware/apiAuth.ts`  | 89   | `createPermissionMiddleware` | used in module |
| `server/middleware/apiAuth.ts`  | 122  | `combinedAuth`               | used in module |
| `server/middleware/apiAuth.ts`  | 175  | `fullApiAccess`              | unused         |
| `server/middleware/security.ts` | 174  | `additionalSecurityHeaders`  | used in module |
| `server/middleware/security.ts` | 191  | `securityLogger`             | used in module |
| `server/middleware/security.ts` | 231  | `securityRateLimit`          | used in module |
| `server/middleware/security.ts` | 274  | `createSimpleRateLimiter`    | used in module |

### Services

| File                                      | Line | Export                    | Status         |
| ----------------------------------------- | ---- | ------------------------- | -------------- |
| `server/services/aiStreamProcessor.ts`    | 22   | `AIStreamProcessor`       | used in module |
| `server/services/apiKeyService.ts`        | 249  | `revokeApiKey`            | unused         |
| `server/services/apiKeyService.ts`        | 301  | `resetFailedAttempts`     | used in module |
| `server/services/apiKeyService.ts`        | 440  | `updateApiKeySettings`    | unused         |
| `server/services/apiKeyService.ts`        | 18   | `ApiKeyData`              | used in module |
| `server/services/apiKeyService.ts`        | 32   | `ApiKeySecurityInfo`      | used in module |
| `server/services/backupService.ts`        | 187  | `createFullBackup`        | unused         |
| `server/services/backupService.ts`        | 208  | `listBackups`             | used in module |
| `server/services/backupService.ts`        | 245  | `cleanupOldBackups`       | used in module |
| `server/services/backupService.ts`        | 270  | `restoreDatabase`         | unused         |
| `server/services/enhancedBidAnalytics.ts` | 640  | `saveRiskAssessment`      | unused         |
| `server/services/enhancedBidAnalytics.ts` | 17   | `CompetitiveIntelligence` | used in module |
| `server/services/enhancedBidAnalytics.ts` | 38   | `EnhancedRiskAssessment`  | used in module |
| `server/services/enhancedBidAnalytics.ts` | 65   | `PredictiveAnalytics`     | used in module |

### File Processing & Storage

| File                                       | Line | Export                    | Status         |
| ------------------------------------------ | ---- | ------------------------- | -------------- |
| `server/services/fileProcessingService.ts` | 122  | `getUploadMiddleware`     | unused         |
| `server/services/fileProcessingService.ts` | 136  | `validateFileContent`     | used in module |
| `server/services/fileProcessingService.ts` | 176  | `compressFileIfNeeded`    | unused         |
| `server/services/fileProcessingService.ts` | 212  | `processLargeFile`        | unused         |
| `server/services/fileProcessingService.ts` | 240  | `extractFileMetadata`     | unused         |
| `server/services/fileProcessingService.ts` | 260  | `calculateBufferChecksum` | used in module |
| `server/services/fileProcessingService.ts` | 268  | `optimizeForStorage`      | unused         |
| `server/services/objectStorageService.ts`  | 249  | `deleteFile`              | unused         |
| `server/services/objectStorageService.ts`  | 266  | `fileExists`              | unused         |
| `server/services/objectStorageService.ts`  | 285  | `getPresignedUrl`         | unused         |
| `server/services/objectStorageService.ts`  | 302  | `listFiles`               | unused         |
| `server/services/objectStorageService.ts`  | 18   | `StoredFile`              | used in module |
| `server/services/objectStorageService.ts`  | 37   | `ChunkedUploadOptions`    | used in module |

### Audit & Notifications

| File                                                | Line | Export                           | Status         |
| --------------------------------------------------- | ---- | -------------------------------- | -------------- |
| `server/services/auditService.ts`                   | 6    | `FileUploadEventData`            | used in module |
| `server/services/auditService.ts`                   | 15   | `BidActionEventData`             | used in module |
| `server/services/auditService.ts`                   | 25   | `RfqCreationEventData`           | used in module |
| `server/services/auditService.ts`                   | 32   | `RfqDistributionEventData`       | used in module |
| `server/services/scheduledNotificationProcessor.ts` | 10   | `ScheduledNotificationProcessor` | used in module |

### Core Services

| File                                       | Line | Export                    | Status         |
| ------------------------------------------ | ---- | ------------------------- | -------------- |
| `server/services/core/pdfExtractor.ts`     | 60   | `UnifiedPDFExtractor`     | used in module |
| `server/services/core/testPdfExtractor.ts` | 10   | `testUnifiedPDFExtractor` | used in module |
| `server/services/core/testPdfExtractor.ts` | 81   | `runPDFExtractionTest`    | unused         |

### Utilities

| File                     | Line | Export          | Status         |
| ------------------------ | ---- | --------------- | -------------- |
| `server/utils/typing.ts` | 8    | `asUpdate`      | unused         |
| `server/utils/typing.ts` | 13   | `withUpdatedAt` | unused         |
| `server/vite.ts`         | 16   | `log`           | used in module |

## Client-Side Unused Exports

### Components

| File                                                 | Line | Export                   | Status         |
| ---------------------------------------------------- | ---- | ------------------------ | -------------- |
| `client/src/components/AdvancedBidFilters.tsx`       | 62   | `default`                | unused         |
| `client/src/components/AdvancedBidFilters.tsx`       | 14   | `AdvancedFilterState`    | used in module |
| `client/src/components/BidLineItemForm.tsx`          | 12   | `BidLineItem`            | used in module |
| `client/src/components/BidManagementWidget.tsx`      | 62   | `BidManagementWidget`    | unused         |
| `client/src/components/BidScopeDefinition.tsx`       | 13   | `ScopeItem`              | unused         |
| `client/src/components/CategoryComparisonView.tsx`   | 94   | `CategoryComparisonView` | unused         |
| `client/src/components/MultiFileUploadInterface.tsx` | 9    | `FileType`               | used in module |
| `client/src/components/PDFViewer.tsx`                | 17   | `PDFViewer`              | unused         |

### Hooks & Utilities

| File                                       | Line | Export                  | Status         |
| ------------------------------------------ | ---- | ----------------------- | -------------- |
| `client/src/hooks/use-toast.ts`            | 74   | `reducer`               | used in module |
| `client/src/hooks/useNotifications.ts`     | 21   | `NotificationsResponse` | used in module |
| `client/src/hooks/useStructuredBidData.ts` | 65   | `calculateDataQuality`  | unused         |
| `client/src/hooks/useStructuredBidData.ts` | 25   | `StructuredBidData`     | used in module |
| `client/src/hooks/useUserRole.ts`          | 5    | `UserRoleType`          | used in module |
| `client/src/lib/authUtils.ts`              | 1    | `isUnauthorizedError`   | unused         |

### Constants & Types

| File                                     | Line | Export                 | Status         |
| ---------------------------------------- | ---- | ---------------------- | -------------- |
| `client/src/constants/certifications.ts` | 1    | `CertificationOption`  | used in module |
| `client/src/constants/trades.ts`         | 1    | `TradeOption`          | used in module |
| `client/src/types/notifications.ts`      | 2    | `NotificationTypes`    | used in module |
| `client/src/types/notifications.ts`      | 26   | `NotificationType`     | unused         |
| `client/src/types/notifications.ts`      | 28   | `NotificationPriority` | unused         |
| `client/src/types/notifications.ts`      | 30   | `NotificationData`     | unused         |

### Providers

| File                                     | Line | Export     | Status |
| ---------------------------------------- | ---- | ---------- | ------ |
| `client/src/providers/TermsProvider.tsx` | 14   | `useTerms` | unused |

### UI Components (Extensive List)

The following UI components appear to be unused or only used internally. These are likely part of a component library:

#### Alert & Dialog Components

- `AlertDialogPortal`, `AlertDialogOverlay`, `AlertDialogAction`, `AlertDialogCancel` - used in module
- `AlertTitle` - used in module

#### Form & Input Components

- `AspectRatio`, `Badge`, `BadgeProps`, `badgeVariants` - used in module
- `InputOTP`, `InputOTPGroup`, `InputOTPSlot`, `InputOTPSeparator` - used in module

#### Navigation & Layout Components

- `Breadcrumb`, `BreadcrumbList`, `BreadcrumbItem`, `BreadcrumbLink`, `BreadcrumbPage`, `BreadcrumbSeparator`, `BreadcrumbEllipsis` - used in module
- `Sidebar` and related components (extensive list) - used in module

#### Data Display Components

- `Calendar`, `Card`, `Carousel`, `Chart`, `Table` components - used in module
- `Command`, `ContextMenu`, `Dialog`, `Drawer` components - used in module

## Recommendations

### High Priority for Removal

1. **Completely Unused Exports** - These can be safely removed:
   - `server/services/apiKeyService.ts`: `revokeApiKey`, `updateApiKeySettings`
   - `server/services/backupService.ts`: `createFullBackup`, `restoreDatabase`
   - `server/services/enhancedBidAnalytics.ts`: `saveRiskAssessment`
   - `server/services/fileProcessingService.ts`: Multiple file processing functions
   - `server/services/objectStorageService.ts`: File operation functions
   - `server/utils/typing.ts`: `asUpdate`, `withUpdatedAt`

2. **Unused Components** - Review and remove if not needed:
   - `BidManagementWidget`, `BidScopeDefinition`, `CategoryComparisonView`
   - `PDFViewer`, `AdvancedBidFilters`

### Medium Priority for Review

1. **"Used in Module" Exports** - Review if these need to be exported:
   - Many middleware and service exports that are only used internally
   - Type definitions that might be over-exported

### Low Priority

1. **UI Components** - These appear to be part of a design system and should be kept unless specifically unused

## Action Items

- [ ] Remove high-priority unused exports
- [ ] Review medium-priority exports for potential un-exporting
- [ ] Update import statements after removals
- [ ] Run tests to ensure no breaking changes
- [ ] Re-run ts-prune to verify cleanup success
