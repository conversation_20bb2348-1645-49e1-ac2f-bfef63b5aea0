import { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Heart, Plus, Trash2, Star, Building2, MapPin, Phone, Mail } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

interface Contractor {
  id: string;
  companyName: string;
  primaryContactName: string;
  primaryContactEmail: string;
  primaryContactPhone: string | null;
  businessAddress: string | null;
  tradeTypes: string[];
  verified: boolean;
}

interface ContractorFavorite {
  id: string;
  contractorId: string;
  notes: string | null;
  addedAt: string;
  contractor?: Contractor;
}

const addFavoriteSchema = z.object({
  contractorId: z.string().min(1, "Please select a contractor"),
  notes: z.string().optional(),
});

type AddFavoriteData = z.infer<typeof addFavoriteSchema>;

export function ContractorFavoritesManager() {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<AddFavoriteData>({
    resolver: zodResolver(addFavoriteSchema),
    defaultValues: {
      contractorId: "",
      notes: "",
    },
  });

  // Fetch favorite contractors
  const { data: favorites = [], isLoading: favoritesLoading } = useQuery<ContractorFavorite[]>({
    queryKey: ['/api/contractors/favorites'],
  });

  // Fetch all contractors for selection
  const { data: allContractors = [], isLoading: contractorsLoading } = useQuery<Contractor[]>({
    queryKey: ['/api/contractors'],
  });

  // Add contractor to favorites
  const addFavoriteMutation = useMutation({
    mutationFn: async (data: AddFavoriteData) => {
      return apiRequest('POST', `/api/contractors/${data.contractorId}/favorite`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/contractors/favorites'] });
      toast({
        title: "Contractor Added to Favorites",
        description: "The contractor has been added to your favorites list.",
      });
      form.reset();
      setIsAddDialogOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: "Failed to Add Favorite",
        description: error.message || "Could not add contractor to favorites.",
        variant: "destructive",
      });
    },
  });

  // Remove contractor from favorites
  const removeFavoriteMutation = useMutation({
    mutationFn: async (contractorId: string) => {
      return apiRequest('DELETE', `/api/contractors/${contractorId}/favorite`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/contractors/favorites'] });
      toast({
        title: "Contractor Removed",
        description: "The contractor has been removed from your favorites.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to Remove Favorite",
        description: error.message || "Could not remove contractor from favorites.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: AddFavoriteData) => {
    addFavoriteMutation.mutate(data);
  };

  // Filter out contractors that are already in favorites
  const availableContractors = allContractors.filter(
    contractor => !favorites.some(fav => fav?.contractorId === contractor.id)
  );

  const getVerificationBadge = (verified: boolean) => {
    return verified ? (
      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
        <Star className="h-3 w-3 mr-1" />
        Verified
      </Badge>
    ) : (
      <Badge variant="outline">
        Pending
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-5 w-5" />
              Favorite Contractors
            </CardTitle>
            <CardDescription>
              Manage your preferred contractors for targeted RFQ distribution
            </CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Favorite
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Contractor to Favorites</DialogTitle>
                <DialogDescription>
                  Select a contractor to add to your favorites list for easy RFQ distribution.
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="contractorId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contractor</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a contractor" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {availableContractors.map((contractor) => (
                              <SelectItem key={contractor.id} value={contractor.id}>
                                <div className="flex items-center gap-2">
                                  <Building2 className="h-4 w-4" />
                                  <span>{contractor.companyName}</span>
                                  {contractor.verified && <Star className="h-3 w-3 text-green-600" />}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notes (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Add notes about this contractor..."
                            {...field}
                            rows={3}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex justify-end gap-3">
                    <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={addFavoriteMutation.isPending}>
                      {addFavoriteMutation.isPending ? "Adding..." : "Add to Favorites"}
                    </Button>
                  </div>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {favoritesLoading ? (
          <div className="animate-pulse space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-muted rounded-lg" />
            ))}
          </div>
        ) : favorites.length === 0 ? (
          <div className="text-center py-8">
            <Heart className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground mb-4">
              You haven't added any favorite contractors yet.
            </p>
            <p className="text-sm text-muted-foreground">
              Add contractors to your favorites for quick RFQ distribution and easy access.
            </p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Company</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Trade Types</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Notes</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {favorites.map((favorite) => {
                if (!favorite.contractor) {
                  return (
                    <TableRow key={favorite.id}>
                      <TableCell colSpan={6} className="text-center text-muted-foreground">
                        Error: Contractor data missing for this favorite
                      </TableCell>
                    </TableRow>
                  );
                }
                
                return (
                  <TableRow key={favorite.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{favorite.contractor?.companyName || 'Unknown Company'}</p>
                          {favorite.contractor?.businessAddress && (
                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {favorite.contractor.businessAddress}
                            </p>
                          )}
                        </div>
                      </div>
                    </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{favorite.contractor?.primaryContactName || 'Unknown Contact'}</p>
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          {favorite.contractor?.primaryContactEmail || 'No email'}
                        </p>
                        {favorite.contractor?.primaryContactPhone && (
                          <p className="text-sm text-muted-foreground flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            {favorite.contractor.primaryContactPhone}
                          </p>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {favorite.contractor?.tradeTypes?.slice(0, 2).map((trade) => (
                        <Badge key={trade} variant="outline" className="text-xs">
                          {trade}
                        </Badge>
                      )) || (
                        <Badge variant="outline" className="text-xs">
                          No trades listed
                        </Badge>
                      )}
                      {favorite.contractor?.tradeTypes?.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{favorite.contractor.tradeTypes.length - 2} more
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {getVerificationBadge(favorite.contractor?.verified || false)}
                  </TableCell>
                  <TableCell>
                    <p className="text-sm text-muted-foreground">
                      {favorite.notes || "No notes"}
                    </p>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeFavoriteMutation.mutate(favorite.contractorId)}
                      disabled={removeFavoriteMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
                );
              })}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}