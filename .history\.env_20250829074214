# Database Configuration
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
PGDATABASE=neondb
PGHOST=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
PGPORT=5432
PGUSER=neondb_owner
PGPASSWORD=npg_hf9NUCnHDkF6

# Clerk Authentication
CLERK_PUBLISHABLE_KEY=pk_test_YXdhaXRlZC1ncmFja2xlLTkwLmNsZXJrLmFjY291bnRzLmRldiQ
CLERK_SECRET_KEY=sk_test_27mlwUK1sgyTyklq76Q73qsnxLIjaXzQrWq5FjumTd
# Vite requires VITE_ prefix for frontend access
VITE_CLERK_PUBLISHABLE_KEY=pk_test_YXdhaXRlZC1ncmFja2xlLTkwLmNsZXJrLmFjY291bnRzLmRldiQ

# AI Services
GROQ_API_KEY=********************************************************
OPENAI_API_KEY=********************************************************************************************************************************************************************
GEMINI_API_KEY=AIzaSyCnWXqrz4s43HEbMj_05T01FoX2xuZeVC0
RESEND_API_KEY=re_Joui4szb_D5NstaT9RTuCjENXqxY8KKDy

# Wasabi Object Storage (NEW)
WASABI_ACCESS_KEY_ID=QMX972IMONHSOMR89UJB
WASABI_SECRET_ACCESS_KEY=QyYDdnXS55U7jB3DK2LXW8CR2LCRT5DFClRXXnFw
WASABI_BUCKET_NAME=bidaible-storage
WASABI_ENDPOINT=https://s3.wasabisys.com
WASABI_REGION=us-east-1

# Application Configuration
NODE_ENV=development
PRIMARY_MODEL=groq
SESSION_SECRET=your_session_secret_here
JWT_SECRET=your_jwt_secret_here

# Optional: For development
PORT=5000

SENTRY_DSN=https://<EMAIL>/4509927037468672 
VITE_SENTRY_DSN=https://<EMAIL>/4509927037468672
