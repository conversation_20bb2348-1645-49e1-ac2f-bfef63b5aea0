// Simple test to check if server receives sessionId and logs appear
const FormData = require('form-data');
const fs = require('fs');

console.log('Testing upload with sessionId...');

// Create a simple text file for testing
const testContent = 'This is a test file for progress tracking';
fs.writeFileSync('./test.txt', testContent);

const form = new FormData();
form.append('sessionId', 'test-session-123');
form.append('uploadBatchId', 'test-batch-456');
form.append('documents', fs.createReadStream('./test.txt'));

fetch('http://localhost:5000/api/rfqs', {
  method: 'POST',
  body: form,
  headers: {
    // This will fail without auth, but we should see server logs
  }
}).then(res => res.text()).then(console.log).catch(console.error);
