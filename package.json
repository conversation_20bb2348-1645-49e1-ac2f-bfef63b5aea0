{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development tsx --import ./server/instrument.mjs server/index.ts", "dev:api": "cross-env NODE_ENV=development BIDAIBLE_TEST=1 tsx --import ./server/instrument.mjs server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "cross-env NODE_ENV=production node dist/index.js", "debug": "node railway-debug.js", "check": "tsc -p tsconfig.server.json", "lint": "eslint . --ext .ts,.tsx,.js", "lint:fix": "eslint . --ext .ts,.tsx,.js --fix", "db:push": "drizzle-kit push", "prune-files": "npx ts-prune"}, "dependencies": {"@aws-sdk/client-s3": "^3.864.0", "@aws-sdk/s3-request-presigner": "^3.864.0", "@clerk/clerk-react": "^5.40.0", "@clerk/express": "^1.7.17", "@google/genai": "^1.10.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@sentry/node": "^10.8.0", "@sentry/react": "^10.8.0", "@tanstack/react-query": "^5.60.5", "@types/archiver": "^6.0.3", "@types/cors": "^2.8.19", "@types/jsonwebtoken": "^9.0.10", "@types/memoizee": "^0.4.12", "@types/multer": "^2.0.0", "@types/react-toastify": "^4.0.2", "archiver": "^7.0.1", "aws-sdk": "^2.1692.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^17.2.1", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "helmet": "^8.1.0", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "multer": "^2.0.1", "nanoid": "^5.1.5", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "openai": "^5.9.1", "pdf-parse": "^1.1.1", "pdf2pic": "^3.2.0", "pdfjs-dist": "^5.3.93", "pino": "^9.3.2", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-markdown": "^10.1.0", "react-pdf": "^10.0.1", "react-resizable-panels": "^2.1.7", "react-toastify": "^11.0.5", "recharts": "^2.15.2", "remark-gfm": "^4.0.1", "resend": "^4.7.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "wouter": "^3.3.5", "ws": "^8.18.3", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.36.0", "@replit/vite-plugin-cartographer": "^0.2.7", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.3", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "cross-env": "^10.0.0", "drizzle-kit": "^0.18.1", "esbuild": "^0.25.0", "eslint": "^9.9.0", "globals": "^16.4.0", "pino-pretty": "^11.2.1", "postcss": "^8.4.47", "prettier": "^3.3.3", "tailwindcss": "^3.4.17", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^6.3.5"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}