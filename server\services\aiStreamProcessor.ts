import { promises as fsp, createReadStream } from "fs";
import { pipeline } from "stream/promises";
import { Transform } from "stream";
import { unifiedPDFExtractor } from "./core/pdfExtractor";
import { UploadStage } from "../../shared/schema";

interface ProcessingOptions {
  chunkSize?: number;
  timeout?: number;
  maxConcurrent?: number;
  onProgress?: (progress: { stage: string; percentage: number; message: string }) => void;
}

interface StreamProcessingResult {
  extractedText: string;
  processingTime: number;
  chunks: number;
  success: boolean;
  error?: string;
}

export class AIStreamProcessor {
  private readonly MAX_CONCURRENT = 8;
  private readonly DEFAULT_TIMEOUT = 30000; // 30 seconds
  private readonly CHUNK_SIZE = 1024 * 1024; // 1MB chunks
  private activeProcessors = new Set<string>();

  /**
   * Process PDF file using stream-based approach
   * NOW USING UNIFIED PDF EXTRACTOR - FIXES 29-CHARACTER REGRESSION
   */
  async processPDFStream(
    filePath: string, 
    options: ProcessingOptions = {}
  ): Promise<StreamProcessingResult> {
    const startTime = Date.now();
    const processingId = `pdf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // Check concurrent processing limit
      if (this.activeProcessors.size >= (options.maxConcurrent || this.MAX_CONCURRENT)) {
        throw new Error('Maximum concurrent processing limit reached');
      }

      this.activeProcessors.add(processingId);
      options.onProgress?.({ stage: UploadStage.TEXT_EXTRACTION, percentage: 10, message: 'Loading PDF document' });

      // Read file buffer
      const fileBuffer = await fsp.readFile(filePath);
      options.onProgress?.({ stage: UploadStage.TEXT_EXTRACTION, percentage: 30, message: 'Processing with unified extractor' });

      // Use unified PDF extractor instead of broken legacy code
      const result = await unifiedPDFExtractor.extractText(fileBuffer, {
        onProgress: (progress) => {
          // Map unified extractor progress to stream processor format
          const adjustedPercentage = 30 + (progress.percentage * 0.7); // 30-100% range
          options.onProgress?.({ 
            stage: progress.stage, 
            percentage: Math.round(adjustedPercentage), 
            message: progress.message 
          });
        }
      });

      const processingTime = Date.now() - startTime;
      options.onProgress?.({ stage: 'complete', percentage: 100, message: 'PDF processing complete' });

      return {
        extractedText: result.text,
        processingTime,
        chunks: 1, // Unified extractor doesn't use chunks
        success: result.success
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`PDF stream processing failed for ${processingId}:`, error);
      
      return {
        extractedText: '',
        processingTime,
        chunks: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      this.activeProcessors.delete(processingId);
    }
  }

  /**
   * Process text file using stream approach
   */
  async processTextStream(
    filePath: string,
    options: ProcessingOptions = {}
  ): Promise<StreamProcessingResult> {
    const startTime = Date.now();
    const processingId = `text_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      if (this.activeProcessors.size >= (options.maxConcurrent || this.MAX_CONCURRENT)) {
        throw new Error('Maximum concurrent processing limit reached');
      }

      this.activeProcessors.add(processingId);
      options.onProgress?.({ stage: 'text_loading', percentage: 20, message: 'Loading text file' });

      let extractedText = '';
      let chunks = 0;

      const textStream = createReadStream(filePath, { 
        encoding: 'utf8',
        highWaterMark: options.chunkSize || this.CHUNK_SIZE 
      });

      const textProcessor = new Transform({
        transform(chunk, encoding, callback) {
          extractedText += chunk.toString();
          chunks++;
          callback();
        }
      });

      await pipeline(textStream, textProcessor);
      
      const processingTime = Date.now() - startTime;
      options.onProgress?.({ stage: 'complete', percentage: 100, message: 'Text processing complete' });

      return {
        extractedText: extractedText.trim(),
        processingTime,
        chunks,
        success: true
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`Text stream processing failed for ${processingId}:`, error);
      
      return {
        extractedText: '',
        processingTime,
        chunks: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      this.activeProcessors.delete(processingId);
    }
  }

  /**
   * Read file in chunks to manage memory usage
   */
  private async readFileInChunks(
    filePath: string, 
    options: ProcessingOptions
  ): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const chunks: Buffer[] = [];
      const stream = createReadStream(filePath, { 
        highWaterMark: options.chunkSize || this.CHUNK_SIZE 
      });

      const timeoutId = setTimeout(() => {
        stream.destroy();
        reject(new Error('File reading timeout'));
      }, options.timeout || this.DEFAULT_TIMEOUT);

      stream.on('data', (chunk: Buffer) => {
        chunks.push(chunk);
      });

      stream.on('end', () => {
        clearTimeout(timeoutId);
        resolve(Buffer.concat(chunks));
      });

      stream.on('error', (error) => {
        clearTimeout(timeoutId);
        reject(error);
      });
    });
  }

  /**
   * Process a batch of PDF pages
   */
  private async processPDFBatch(
    pdfDoc: any, 
    startPage: number, 
    endPage: number
  ): Promise<string> {
    let batchText = '';
    
    for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
      try {
        const page = await pdfDoc.getPage(pageNum);
        const textContent = await page.getTextContent();
        
        console.log(`🔍 STREAM Page ${pageNum} - textContent.items:`, {
          length: textContent.items?.length || 0,
          firstItem: textContent.items?.[0],
          sampleItems: textContent.items?.slice(0, 5),
          allItemsStructure: textContent.items?.map((item, idx) => ({
            index: idx,
            type: typeof item,
            properties: Object.keys(item || {}),
            str: item?.str,
            text: item?.text,
            textContent: item?.textContent,
            content: item?.content,
            value: item?.value
          })).slice(0, 10) // First 10 items detailed
        });
        
        const pageText = this.extractTextFromItems(textContent.items);
        console.log(`🔍 STREAM Page ${pageNum} - Extracted text length: ${pageText.length}`);
        console.log(`🔍 STREAM Page ${pageNum} - Text preview: "${pageText.substring(0, 200)}..."`);
        
        // Try alternative extraction if no text found
        if (pageText.length === 0 && textContent.items?.length > 0) {
          console.log(`🔧 STREAM Page ${pageNum} - Trying alternative extraction methods`);
          
          const directText = textContent.items?.map((item: any) => {
            return item?.str || item?.text || item?.textContent || item?.content || item?.value || '';
          }).filter(text => text && text.trim()).join(' ') || '';
          
          console.log(`🔧 STREAM Page ${pageNum} - Direct extraction length: ${directText.length}`);
          console.log(`🔧 STREAM Page ${pageNum} - Direct text preview: "${directText.substring(0, 200)}..."`);
          
          if (directText.length > 0) {
            batchText += `\n\n--- Page ${pageNum} ---\n${directText}`;
          } else {
            batchText += `\n\n--- Page ${pageNum} ---\n`;
          }
        } else {
          batchText += `\n\n--- Page ${pageNum} ---\n${pageText}`;
        }
      } catch (pageError) {
        console.warn(`Error processing page ${pageNum}:`, pageError);
        batchText += `\n\n--- Page ${pageNum} (Error) ---\n`;
      }
    }
    
    return batchText;
  }

  /**
   * Robust text extraction from PDF.js text content items
   * Handles various PDF.js text item structures
   */
  private extractTextFromItems(items: any[]): string {
    console.log('🔍 CRITICAL DEBUG - extractTextFromItems called in aiStreamProcessor.ts');
    console.log('🔍 Items array length:', items?.length || 0);
    console.log('🔍 Items array type:', Array.isArray(items) ? 'Array' : typeof items);
    console.log('🔍 First 3 items structure:', items?.slice(0, 3)?.map((item, index) => ({
      index,
      type: typeof item,
      keys: item && typeof item === 'object' ? Object.keys(item) : [],
      str: item?.str,
      text: item?.text,
      textContent: item?.textContent,
      content: item?.content,
      value: item?.value,
      transform: item?.transform,
      width: item?.width,
      height: item?.height,
      fontName: item?.fontName,
      hasEOL: item?.hasEOL,
      fullItem: item
    })));
    
    if (!items || !Array.isArray(items)) {
      console.log('🚨 Items is not an array, returning empty string');
      return '';
    }

    const textParts: string[] = [];
    
    for (const item of items) {
      try {
        let text = '';
        
        // Handle different PDF.js text item structures
        if (typeof item === 'string') {
          text = item;
          console.log('🔍 Found string item:', text);
        } else if (item && typeof item === 'object') {
          // Most common: item.str property
          if (typeof item.str === 'string') {
            text = item.str;
            console.log('🔍 Found item.str:', text);
          }
          // Alternative: item.text property
          else if (typeof item.text === 'string') {
            text = item.text;
            console.log('🔍 Found item.text:', text);
          }
          // Handle nested text objects
          else if (item.textContent && typeof item.textContent === 'string') {
            text = item.textContent;
            console.log('🔍 Found item.textContent:', text);
          }
          // Handle array of text items
          else if (Array.isArray(item.items)) {
            text = this.extractTextFromItems(item.items);
            console.log('🔍 Found nested items, extracted:', text);
          }
          // Try more properties as recommended in debugging plan
          else if (typeof item.content === 'string') {
            text = item.content;
            console.log('🔍 Found item.content:', text);
          }
          else if (typeof item.value === 'string') {
            text = item.value;
            console.log('🔍 Found item.value:', text);
          }
          else if (typeof item.unicode === 'string') {
            text = item.unicode;
            console.log('🔍 Found item.unicode:', text);
          }
          else if (typeof item.chars === 'string') {
            text = item.chars;
            console.log('🔍 Found item.chars:', text);
          }
          // Fallback: try to stringify if it contains meaningful data
          else if (item.width && item.height && item.transform) {
            // This might be a malformed text item, skip it
            console.log('🔍 Skipping geometric item (no text):', Object.keys(item));
            continue;
          } else {
            console.log('🚨 Item has no recognizable text property:', Object.keys(item));
          }
        }
        
        // Clean and add text if we found any
        if (text && typeof text === 'string') {
          text = text.trim();
          if (text.length > 0) {
            textParts.push(text);
          }
        }
      } catch (itemError) {
        // Skip malformed items but don't break the entire extraction
        console.warn('Error extracting text from PDF item:', itemError);
        continue;
      }
    }
    
    return textParts.join(' ');
  }

  /**
   * Get current processing statistics
   */
  getProcessingStats() {
    return {
      activeProcessors: this.activeProcessors.size,
      maxConcurrent: this.MAX_CONCURRENT,
      availableSlots: this.MAX_CONCURRENT - this.activeProcessors.size
    };
  }

  /**
   * Clean up resources and cancel active processing
   */
  async cleanup(): Promise<void> {
    console.log(`Cleaning up ${this.activeProcessors.size} active processors`);
    this.activeProcessors.clear();
  }
}

// Singleton instance
export const streamProcessor = new AIStreamProcessor();