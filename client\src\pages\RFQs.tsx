import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { RFQForm } from "@/components/RFQForm";
import { RFQDetailsModal } from "@/components/RFQDetailsModal";
import { BidSubmissionForm } from "@/components/BidSubmissionForm";
import { DocumentCount } from "@/components/DocumentCount";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertDialog, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Search, Filter, Calendar, MapPin, Clock, Users, FileText, Eye, Send, Archive, Download, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { CountdownBadge, DualCountdownBadges } from "@/components/CountdownBadge";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";

interface Rfq {
  id: string;
  createdBy?: string | null;
  organizationId: string;
  projectName: string;
  projectLocation: string;
  tradeCategory: string;
  description: string | null;
  dueDate: string;
  bidProposalDeadlineAt: string;
  status: string;
  extractedData?: any;
  aiSummary?: string | null;
  bufferPercentage?: string | null;
  bufferNotes?: string | null;
  createdAt?: string | null;
  updatedAt?: string | null;
  isArchived?: boolean;
}
 
export default function RFQs() {
  const [, navigate] = useLocation();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedRfq, setSelectedRfq] = useState<any>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showBidForm, setShowBidForm] = useState(false);
  const [bidRfq, setBidRfq] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [tradeFilter, setTradeFilter] = useState("all");
  const [showArchived, setShowArchived] = useState(false);
  const [selectedRfqs, setSelectedRfqs] = useState<Set<string>>(new Set());
  const [bulkMode, setBulkMode] = useState(false);
  
  const queryClient = useQueryClient();

  const { data: rfqs = [], isLoading, error } = useQuery<Rfq[]>({
    queryKey: showArchived ? ["/api/rfqs/archived"] : ["/api/rfqs"],
  });

  // Debug logging for archived RFQs
  useEffect(() => {
    if (showArchived) {
      console.log("Archived RFQs query:", { data: rfqs, isLoading, error });
    }
  }, [rfqs, isLoading, error, showArchived]);

  // Clear selection when switching between archived/normal view
  useEffect(() => {
    setSelectedRfqs(new Set());
    setBulkMode(false);
  }, [showArchived]);

  // Get document counts for each RFQ
  const rfqsWithDocumentCounts = rfqs.map((rfq: Rfq) => ({
    ...rfq,
    documentCount: 0 // This will be populated by individual queries or optimized backend call
  }));

  // Listen for Create RFQ events from sidebar
  useEffect(() => {
    const handleOpenCreateRFQ = () => {
      setShowCreateForm(true);
    };

    // Check URL params on load
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('create') === 'true') {
      setShowCreateForm(true);
      // Clean up URL
      urlParams.delete('create');
      const newUrl = `${window.location.pathname}${urlParams.toString() ? '?' + urlParams.toString() : ''}`;
      window.history.replaceState({}, '', newUrl);
    }

    window.addEventListener('openCreateRFQ', handleOpenCreateRFQ);
    return () => window.removeEventListener('openCreateRFQ', handleOpenCreateRFQ);
  }, []);

  // Archive handler functions
  const handleArchive = async (rfqId: string) => {
    try {
      const response = await fetch(`/api/rfqs/${rfqId}/archive`, {
        method: 'PATCH',
        credentials: 'include',
      });
      
      if (response.ok) {
        toast.success('RFQ archived successfully');
        queryClient.invalidateQueries({ queryKey: ["/api/rfqs"] });
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to archive RFQ');
      }
    } catch (error) {
      toast.error('Failed to archive RFQ');
    }
  };

  const handleBulkArchive = async () => {
    try {
      const response = await fetch('/api/rfqs/bulk-archive', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ rfqIds: Array.from(selectedRfqs) }),
      });
      
      if (response.ok) {
        const result = await response.json();
        toast.success(`${result.archivedCount} RFQ${result.archivedCount === 1 ? '' : 's'} archived successfully`);
        setSelectedRfqs(new Set());
        setBulkMode(false);
        queryClient.invalidateQueries({ queryKey: ["/api/rfqs"] });
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to archive RFQs');
      }
    } catch (error) {
      toast.error('Failed to archive RFQs');
    }
  };

  const handleUnarchive = async (rfqId: string) => {
    try {
      const response = await fetch(`/api/rfqs/${rfqId}/unarchive`, {
        method: 'PATCH',
        credentials: 'include',
      });
      
      if (response.ok) {
        toast.success('RFQ unarchived successfully');
        queryClient.invalidateQueries({ queryKey: ["/api/rfqs/archived"] });
        queryClient.invalidateQueries({ queryKey: ["/api/rfqs"] });
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to unarchive RFQ');
      }
    } catch (error) {
      toast.error('Failed to unarchive RFQ');
    }
  };

  const handleDownloadArchive = async (rfqId: string, projectName: string) => {
    try {
      const response = await fetch(`/api/rfqs/${rfqId}/archive-download`, {
        method: 'GET',
        credentials: 'include',
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `RFQ-${projectName.replace(/[^a-zA-Z0-9-_]/g, '-')}-Archive-${new Date().toISOString().split('T')[0]}.zip`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success('Archive downloaded successfully');
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to download archive');
      }
    } catch (error) {
      toast.error('Failed to download archive');
    }
  };

  const filteredRfqs: Rfq[] = rfqs.filter((rfq: Rfq) => {
    const matchesSearch = (rfq.projectName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (rfq.projectLocation || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || rfq.status === statusFilter;
    const matchesTrade = tradeFilter === "all" || rfq.tradeCategory === tradeFilter;

    return matchesSearch && matchesStatus && matchesTrade;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
      case "Draft":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
      case "Review":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";
      case "Closed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
    }
  };

  const getTradeColor = (trade: string) => {
    switch (trade) {
      case "electrical":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";
      case "plumbing":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
      case "hvac":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
      case "concrete":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
      default:
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">My RFQs</h1>
          <p className="text-muted-foreground">
            Manage your construction requests for quotes
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search RFQs..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="Draft">Draft</SelectItem>
            <SelectItem value="Active">Active</SelectItem>
            <SelectItem value="Review">Review</SelectItem>
            <SelectItem value="Closed">Closed</SelectItem>
          </SelectContent>
        </Select>
        <Select value={tradeFilter} onValueChange={setTradeFilter}>
          <SelectTrigger className="w-[180px]">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Filter by trade" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Trades</SelectItem>
            <SelectItem value="electrical">Electrical</SelectItem>
            <SelectItem value="plumbing">Plumbing</SelectItem>
            <SelectItem value="hvac">HVAC</SelectItem>
            <SelectItem value="concrete">Concrete</SelectItem>
            <SelectItem value="general">General</SelectItem>
          </SelectContent>
        </Select>
        
        {/* Archive Toggle */}
        <div className="flex items-center space-x-2">
          <Switch
            id="show-archived"
            checked={showArchived}
            onCheckedChange={setShowArchived}
          />
          <Label htmlFor="show-archived" className="text-sm font-medium">
            Show Archived
          </Label>
        </div>

        {/* Bulk Operations Toggle - Hidden for now */}
        {/* {!showArchived && (
          <div className="flex items-center space-x-2">
            <Switch
              id="bulk-mode"
              checked={bulkMode}
              onCheckedChange={setBulkMode}
            />
            <Label htmlFor="bulk-mode" className="text-sm font-medium">
              Bulk Select
            </Label>
          </div>
        )} */}
      </div>

      {/* Bulk Archive Toolbar */}
      {bulkMode && selectedRfqs.size > 0 && (
        <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">
              {selectedRfqs.size} RFQ{selectedRfqs.size === 1 ? '' : 's'} selected
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setSelectedRfqs(new Set())}
            >
              Clear Selection
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="default" size="sm">
                  <Archive className="w-4 h-4 mr-2" />
                  Archive Selected
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Archive RFQs</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to archive {selectedRfqs.size} RFQ{selectedRfqs.size === 1 ? '' : 's'}? 
                    They will be moved to the archived section and hidden from contractors.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <Button variant="outline">Cancel</Button>
                  <Button onClick={() => handleBulkArchive()}>Archive</Button>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      )}

      {/* RFQ Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="h-4 bg-muted rounded w-full"></div>
                <div className="h-4 bg-muted rounded w-2/3"></div>
                <div className="flex justify-between">
                  <div className="h-6 bg-muted rounded w-16"></div>
                  <div className="h-6 bg-muted rounded w-16"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredRfqs.map((rfq) => (
                  <Card key={rfq.id} className="hover:shadow-md transition-shadow flex flex-col">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3 flex-1 min-w-0">
                          {/* Bulk Selection Checkbox */}
                          {bulkMode && !showArchived && (
                            <Checkbox
                              checked={selectedRfqs.has(rfq.id)}
                              onCheckedChange={(checked) => {
                                const newSelected = new Set(selectedRfqs);
                                if (checked) {
                                  newSelected.add(rfq.id);
                                } else {
                                  newSelected.delete(rfq.id);
                                }
                                setSelectedRfqs(newSelected);
                              }}
                              className="mt-1"
                            />
                          )}
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <CardTitle className="text-lg font-semibold truncate">
                                {rfq.projectName}
                              </CardTitle>
                              {showArchived && (
                                <Badge variant="secondary" className="text-xs">
                                  <Archive className="w-3 h-3 mr-1" />
                                  Archived
                                </Badge>
                              )}
                            </div>
                            <CardDescription className="text-sm text-muted-foreground mt-1">
                              {rfq.projectLocation || "Location TBD"}
                            </CardDescription>
                          </div>
                        </div>
                        <div className="flex flex-col items-end gap-2">
                          <Badge className={getStatusColor(rfq.status)}>
                            {rfq.status}
                          </Badge>
                          
                          {/* Archive button in header for non-archived RFQs */}
                          {!showArchived && !bulkMode && (
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Badge variant="outline" className="cursor-pointer hover:bg-orange-100 hover:border-orange-300 text-muted-foreground hover:text-orange-700">
                                  Archive
                                </Badge>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Archive RFQ</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to archive "{rfq.projectName}"? 
                                    It will be moved to the archived section and hidden from contractors.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <Button variant="outline">Cancel</Button>
                                  <Button onClick={() => handleArchive(rfq.id)}>Archive</Button>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4 flex-1 flex flex-col">
                      <div className="text-sm text-muted-foreground line-clamp-2">
                        {rfq.description || "No description available"}
                      </div>

                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>Due {format(new Date(rfq.dueDate), 'MMM dd, yyyy')}</span>
                        </div>
                        <Badge variant="outline" className={getTradeColor(rfq.tradeCategory)}>
                          {rfq.tradeCategory?.replace('_', ' ') || 'General'}
                        </Badge>
                      </div>

                      {/* Countdown Timers */}
                      {rfq.bidProposalDeadlineAt ? (
                        <DualCountdownBadges
                          bidProposalDeadline={rfq.bidProposalDeadlineAt}
                          rfqDeadline={rfq.dueDate}
                          className="mt-3"
                        />
                      ) : (
                        <div className="mt-3">
                          <CountdownBadge
                            targetDate={rfq.dueDate}
                            label="RFQ closes in"
                            format="short"
                          />
                        </div>
                      )}

                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <DocumentCount rfqId={rfq.id} />
                      </div>

                      <div className="flex flex-col gap-2 mt-auto">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="w-full" 
                          onClick={() => {
                            setSelectedRfq(rfq);
                            setShowDetailsModal(true);
                          }}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View Details
                        </Button>

                        {showArchived ? (
                          <>
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="w-full" 
                              onClick={() => handleUnarchive(rfq.id)}
                            >
                              <Archive className="h-4 w-4 mr-1" />
                              Unarchive
                            </Button>
                            <Button 
                              variant="default" 
                              size="sm" 
                              className="w-full" 
                              onClick={() => handleDownloadArchive(rfq.id, rfq.projectName)}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Download Archive
                            </Button>
                          </>
                        ) : (
                          <>
                            <Button 
                              variant="default" 
                              size="sm" 
                              className="w-full" 
                              onClick={() => navigate(`/rfq/${rfq.id}`)}
                            >
                              <Users className="h-4 w-4 mr-1" />
                              Manage RFQ
                            </Button>
                          </>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
      )}

      {/* Empty State */}
      {!isLoading && filteredRfqs.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
              <Plus className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">No RFQs found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm || statusFilter !== "all" || tradeFilter !== "all" 
                ? "Try adjusting your filters or search terms"
                : showArchived 
                  ? "No archived RFQs found"
                  : "Create your first RFQ to get started"
              }
            </p>
            {!showArchived && (
              <Button onClick={() => setShowCreateForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create RFQ
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Create RFQ Modal */}
      <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto" aria-describedby="rfq-form-description">
          <DialogTitle className="sr-only">Create New RFQ</DialogTitle>
          <div id="rfq-form-description" className="sr-only">
            Upload construction documents to create a new Request for Quote using AI extraction
          </div>
          <RFQForm onClose={() => setShowCreateForm(false)} />
        </DialogContent>
      </Dialog>

      {/* RFQ Details Modal */}
      <RFQDetailsModal 
        rfq={selectedRfq}
        open={showDetailsModal}
        onClose={() => {
          setShowDetailsModal(false);
          setSelectedRfq(null);
        }}
        onEdit={() => {
          // For now, just show details. Can implement edit form later
          console.log("Edit RFQ:", selectedRfq?.id);
        }}
      />

      {/* Bid Submission Modal */}
      {showBidForm && bidRfq && (
        <Dialog open={showBidForm} onOpenChange={setShowBidForm}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto" aria-describedby="bid-form-description">
            <div id="bid-form-description" className="sr-only">
              Submit your bid proposal including documents and specifications
            </div>
            <DialogHeader>
              <DialogTitle>Submit Bid - {bidRfq.projectName}</DialogTitle>
              <DialogDescription>
                Upload your bid package and review the extracted information before submission.
              </DialogDescription>
            </DialogHeader>
            <BidSubmissionForm 
              rfqId={bidRfq.id}
              onSuccess={() => {
                setShowBidForm(false);
                setBidRfq(null);
              }}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}